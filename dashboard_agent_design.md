# Dashboard Agent: Design for Command-Line Interface (CLI) Display

## 1. Overview

The Dashboard Agent (DA) is responsible for providing a real-time, continuously updated command-line interface (CLI) display of the Kraken Multi-Agent Cryptocurrency Trading System's operational status, performance metrics, and critical alerts. Its primary goal is to ensure the user has constant visibility into the system's health and trading activities without interrupting the core trading operations. The DA runs in a separate thread or process to maintain responsiveness and avoid blocking other agents.

## 2. Core Responsibilities

The Dashboard Agent's responsibilities include:

-   **Data Consumption**: Receiving status updates, trade logs, and performance metrics from other agents (primarily the Risk & Execution Agent and potentially the Market Data Agent).
-   **Real-time Display**: Presenting this information in a clear, concise, and continuously updated CLI format.
-   **User Visibility**: Ensuring the user has immediate access to critical information such as operating mode, equity, P/L, drawdown, API status, open positions, and system logs.
-   **Non-Blocking Operation**: Operating asynchronously to prevent any impact on the low-latency trading operations.

## 3. CLI Dashboard Layout and Components

The CLI dashboard will be designed for readability and quick information assimilation. It will be structured into several distinct sections, each dedicated to a specific category of information. The display will be refreshed periodically (e.g., every 1-2 seconds) to reflect the latest data.

### Proposed Layout Structure

```
================================================================================
KRAKEN MULTI-AGENT TRADING SYSTEM - REAL-TIME DASHBOARD
================================================================================

OPERATING MODE: [Trend-Following / Scalping] | PAIR: [XBT/USD]

--------------------------------------------------------------------------------
PORTFOLIO SUMMARY
--------------------------------------------------------------------------------
TOTAL EQUITY:             [USD X,XXX.XX]
REALIZED P/L (24h):       [USD X.XX / X.XX%]
UNREALIZED P/L:           [USD X.XX / X.XX%]
MAX DRAWDOWN LIMIT:       [X.XX%]
CURRENT DRAWDOWN:         [X.XX%]

--------------------------------------------------------------------------------
API STATUS
--------------------------------------------------------------------------------
Key 1 (XXXXX): ACTIVE     | Last Used: [HH:MM:SS]
Key 2 (YYYYY): QUARANTINED | Until: [HH:MM:SS]
Key 3 (ZZZZZ): DISABLED   | Reason: [Invalid Key]

--------------------------------------------------------------------------------
OPEN POSITIONS
--------------------------------------------------------------------------------
PAIR      | TYPE | VOLUME    | ENTRY PRICE | CURRENT PRICE | UNREALIZED P/L
----------|------|-----------|-------------|---------------|---------------
XBT/USD   | LONG | 0.05000   | 20,000.00   | 20,050.00     | +2.50 (+0.25%)
ETH/USD   | SHORT| 0.10000   | 1,500.00    | 1,490.00      | +1.00 (+0.10%)

--------------------------------------------------------------------------------
SYSTEM LOG
--------------------------------------------------------------------------------
[HH:MM:SS] [INFO] MarketDataAgent: Connected to WebSocket.
[HH:MM:SS] [WARNING] RiskExecutionAgent: VOLATILITY HALT for XBT/USD! Cooling down for 15 minutes.
[HH:MM:SS] [CRITICAL] RiskExecutionAgent: MASTER CIRCUIT BREAKER TRIGGERED! Trading Halted.
[HH:MM:SS] [INFO] StrategySignalAgent: Generated BUY signal for XBT/USD.
[HH:MM:SS] [ERROR] RiskExecutionAgent: Failed to place order: Insufficient funds.
```

### Component Breakdown:

1.  **Header**: Displays the system title and a clear indication of its real-time nature.
2.  **Operating Mode & Pair**: Shows the currently active trading mode (Trend-Following or Scalping) and the primary trading pair(s) being monitored. This provides immediate context to the user.
3.  **Portfolio Summary**: This section provides a high-level overview of the user's financial status within the system.
    -   `TOTAL EQUITY`: The current total value of the portfolio, including cash and open positions.
    -   `REALIZED P/L (24h)`: Profit/Loss from closed trades over the last 24 hours, both in absolute currency and percentage terms.
    -   `UNREALIZED P/L`: Current profit/loss from open positions, both in absolute currency and percentage terms.
    -   `MAX DRAWDOWN LIMIT`: The user-defined maximum percentage drawdown allowed for the total portfolio.
    -   `CURRENT DRAWDOWN`: The current percentage drawdown from the peak equity, indicating how far the portfolio has fallen from its highest point.
4.  **API STATUS**: This is a crucial new section providing transparency into the API management framework.
    -   Lists each configured API key (masked for security, e.g., `XXXXX`).
    -   Displays the current `status` of each key (`ACTIVE`, `QUARANTINED`, `DISABLED`).
    -   For `QUARANTINED` keys, it shows the `Until` timestamp when it will be re-activated.
    -   For `DISABLED` keys, it provides a brief `Reason` for its deactivation.
    -   `Last Used`: Timestamp of the last successful API call using that key (optional, but useful for debugging).
5.  **OPEN POSITIONS**: A table detailing all currently open trading positions.
    -   `PAIR`: The trading pair (e.g., XBT/USD).
    -   `TYPE`: `LONG` or `SHORT`.
    -   `VOLUME`: The quantity of the asset held.
    -   `ENTRY PRICE`: The average price at which the position was entered.
    -   `CURRENT PRICE`: The current market price of the asset.
    -   `UNREALIZED P/L`: The current profit or loss for that specific position, both in absolute and percentage terms.
6.  **SYSTEM LOG**: A scrolling log display showing recent system events, warnings, errors, and informational messages from all agents. This provides a chronological record of system activity and helps in diagnosing issues.
    -   Each log entry will include a timestamp and the originating agent.
    -   Different log levels (INFO, WARNING, ERROR, CRITICAL) will be clearly distinguishable.

## 4. Data Flow to Dashboard Agent

The Dashboard Agent will receive updates from other agents via dedicated `asyncio.Queue` objects. This asynchronous communication ensures that the DA does not introduce latency into the critical paths of other agents.

-   **Risk & Execution Agent**: Will push updates related to:
    -   Portfolio equity changes.
    -   Realized and unrealized P/L updates.
    -   Order placement, modification, and cancellation events.
    -   Position changes (open, close, size adjustments).
    -   API key status changes (active, quarantined, disabled).
    -   Rate limit warnings/status.
    -   Circuit breaker and volatility halt triggers.
-   **Market Data Agent**: Could potentially push updates related to:
    -   WebSocket connection status (connected/disconnected).
    -   Significant price movements (for volatility halt detection).
-   **Strategy & Signal Agent**: Could push notifications about:
    -   Strategy activation/deactivation.
    -   Signal generation (for logging purposes, not for direct display of every signal).

## 5. Implementation Considerations

-   **Terminal Manipulation**: Libraries like `curses` or `blessed` can be used for advanced terminal manipulation to create a dynamic, refreshing display without simply printing new lines. This allows for overwriting previous content, creating a true 


interactive dashboard. Alternatively, simpler methods involving clearing the screen and re-printing can be used for basic functionality.
-   **Asynchronous Updates**: The DA will use `asyncio` to consume data from queues and update the display without blocking the main event loop.
-   **Logging Integration**: The system log section will consume messages from a centralized logging handler, allowing all agents to contribute to the displayed log.
-   **Configuration**: The DA will be configurable for display refresh rates, log levels, and potentially which trading pairs to highlight.

This design provides a comprehensive plan for building a user-friendly and informative CLI dashboard for the Kraken Multi-Agent Trading System.

