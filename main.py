import asyncio
import logging
import os
from dotenv import load_dotenv

# Import agents
from src.agents.market_data_agent import MarketDataAgent
from src.agents.strategy_signal_agent import StrategySignalAgent
from src.agents.risk_execution_agent import RiskExecutionAgent, KrakenAPIClient
from src.agents.dashboard_agent import DashboardAgent, log_queue # Import log_queue from dashboard_agent

logging.basicConfig(level=logging.INFO, format=\'%(asctime)s - %(levelname)s - %(message)s\')

async def main():
    load_dotenv() # Load environment variables from .env file

    # Configuration from environment variables
    KRAKEN_API_KEY = os.getenv("KRAKEN_API_KEY")
    KRAKEN_API_SECRET = os.getenv("KRAKEN_API_SECRET")
    KRAKEN_SANDBOX_API_KEY = os.getenv("KRAKEN_SANDBOX_API_KEY")
    KRAKEN_SANDBOX_API_SECRET = os.getenv("KRAKEN_SANDBOX_API_SECRET")
    USE_SANDBOX = os.getenv("USE_SANDBOX", "True").lower() == "true"
    MAX_PORTFOLIO_DRAWDOWN = float(os.getenv("MAX_PORTFOLIO_DRAWDOWN", 0.50))
    OPERATING_MODE = os.getenv("OPERATING_MODE", "trend_following")
    TRADING_PAIRS = os.getenv("TRADING_PAIRS", "XBT/USD,ETH/USD").split(",")

    if USE_SANDBOX:
        logging.info("Running in PAPER TRADING mode using Kraken Sandbox API.")
        api_keys = [(KRAKEN_SANDBOX_API_KEY, KRAKEN_SANDBOX_API_SECRET)]
        # In a real scenario, Kraken sandbox might have a different URL
        # For this simulation, we'll assume the same websocket URL but different API keys
        websocket_url = "wss://ws.kraken.com/"
    else:
        logging.info("Running in LIVE TRADING mode using Kraken Production API.")
        api_keys = [(KRAKEN_API_KEY, KRAKEN_API_SECRET)]
        websocket_url = "wss://ws.kraken.com/"

    # --- Initialize Queues ---
    # Queue for market data from MDA to SSA
    market_data_book_q = asyncio.Queue()
    market_data_ohlc_q = asyncio.Queue()
    # Queue for signals from SSA to REA
    signal_q = asyncio.Queue()
    # Queue for updates from REA to DashboardAgent
    dashboard_q = asyncio.Queue()

    # --- Initialize Agents ---
    logging.info("Initializing Market Data Agent...")
    mda = MarketDataAgent(websocket_url=websocket_url)
    mda.data_queues["book"] = market_data_book_q
    mda.data_queues["ohlc"] = market_data_ohlc_q

    logging.info("Initializing Strategy & Signal Agent...")
    ssa = StrategySignalAgent(mda, OPERATING_MODE, TRADING_PAIRS)
    ssa.signal_queue = signal_q

    logging.info("Initializing Risk & Execution Agent...")
    rea = RiskExecutionAgent(signal_q, dashboard_q, api_keys, MAX_PORTFOLIO_DRAWDOWN)
    # Override the KrakenAPIClient to use a sandbox-aware version if needed
    # For this simulation, the KrakenAPIClient is already a dummy one.
    # In a real system, you'd pass a flag or a different client class.

    logging.info("Initializing Dashboard Agent...")
    # Pass the global log_queue to the DashboardAgent
    dashboard_agent = DashboardAgent(dashboard_q, log_queue)

    # --- Start Agents as asyncio tasks ---
    logging.info("Starting agents...")
    tasks = [
        asyncio.create_task(mda.run()),
        asyncio.create_task(ssa.run()),
        asyncio.create_task(rea.run()),
        asyncio.create_task(dashboard_agent.run())
    ]

    # Simulate initial dashboard updates (e.g., initial equity, operating mode)
    await dashboard_q.put({"type": "initial_equity", "value": 10000.0, "max_drawdown_limit": MAX_PORTFOLIO_DRAWDOWN})
    await dashboard_q.put({"type": "operating_mode", "mode": OPERATING_MODE})
    for i, (key, secret) in enumerate(api_keys):
        await dashboard_q.put({"type": "api_status_update", "key": f"key{i+1}", "status": "ACTIVE"})

    # Keep the main loop running indefinitely
    await asyncio.gather(*tasks)

if __name__ == "__main__":
    # Create a dummy .env file for testing purposes if it doesn't exist
    if not os.path.exists(".env"):
        with open(".env", "w") as f:
            f.write("KRAKEN_API_KEY=your_production_api_key\n")
            f.write("KRAKEN_API_SECRET=your_production_api_secret\n")
            f.write("KRAKEN_SANDBOX_API_KEY=your_sandbox_api_key\n")
            f.write("KRAKEN_SANDBOX_API_SECRET=your_sandbox_api_secret\n")
            f.write("USE_SANDBOX=True\n")
            f.write("MAX_PORTFOLIO_DRAWDOWN=0.50\n")
            f.write("OPERATING_MODE=trend_following\n")
            f.write("TRADING_PAIRS=XBT/USD,ETH/USD\n")
        logging.info("Created a dummy .env file. Please populate with actual Kraken API keys.")

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info("System stopped by user.")
    except Exception as e:
        logging.error(f"An unexpected error occurred: {e}")


