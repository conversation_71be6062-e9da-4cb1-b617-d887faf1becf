import asyncio
import os

# Import agents
from src.agents.market_data_agent import MarketDataAgent
from src.agents.strategy_signal_agent import StrategySignalAgent
from src.agents.risk_execution_agent import RiskExecutionAgent
from src.agents.dashboard_agent import DashboardAgent
from src.utils.logging_config import setup_logging, get_log_queue
from src.utils.config import load_config

async def main():
    """Main entry point for the Kraken Multi-Agent Trading System"""

    # Initialize logging
    logger = setup_logging()
    logger.info("Starting Kraken Multi-Agent Cryptocurrency Trading System...")

    try:
        # Load configuration
        config = load_config()
        logger.info(f"Configuration loaded - Mode: {config.operating_mode}, "
                   f"API Keys: {len(config.kraken_api_keys)}, Pairs: {config.trading_pairs}")

        # Get API keys for live trading
        api_keys = config.get_api_keys()

        logger.warning("🔴 LIVE TRADING MODE - REAL MONEY AT RISK 🔴")
        logger.warning(f"Using {len(api_keys)} Kraken API keys for live trading")
        logger.warning("⚠️  ALL TRADES WILL USE REAL MONEY ⚠️")

        websocket_url = config.websocket_url

        # --- Initialize Queues ---
        logger.info("Initializing communication queues...")
        # Queue for market data from MDA to SSA
        market_data_book_q = asyncio.Queue()
        market_data_ohlc_q = asyncio.Queue()
        # Queue for signals from SSA to REA
        signal_q = asyncio.Queue()
        # Queue for updates from REA to DashboardAgent
        dashboard_q = asyncio.Queue()

        # --- Initialize Agents ---
        logger.info("Initializing Market Data Agent...")
        mda = MarketDataAgent(websocket_url=websocket_url)
        mda.data_queues["book"] = market_data_book_q
        mda.data_queues["ohlc"] = market_data_ohlc_q

        logger.info("Initializing Strategy & Signal Agent...")
        ssa = StrategySignalAgent(mda, config.operating_mode, config.trading_pairs)
        ssa.signal_queue = signal_q

        logger.info("Initializing Risk & Execution Agent...")
        rea = RiskExecutionAgent(
            signal_q,
            dashboard_q,
            api_keys,
            config.max_portfolio_drawdown
        )

        logger.info("Initializing Dashboard Agent...")
        # Use the global log queue from logging config
        dashboard_agent = DashboardAgent(dashboard_q, get_log_queue())

        # --- Start Agents as asyncio tasks ---
        logger.info("Starting agents...")
        tasks = [
            asyncio.create_task(mda.run()),
            asyncio.create_task(ssa.run()),
            asyncio.create_task(rea.run()),
            asyncio.create_task(dashboard_agent.run())
        ]

        # Send initial dashboard updates
        logger.info("Sending initial dashboard updates...")
        await dashboard_q.put({
            "type": "initial_equity",
            "value": config.initial_portfolio_value,
            "max_drawdown_limit": config.max_portfolio_drawdown
        })
        await dashboard_q.put({
            "type": "operating_mode",
            "mode": f"{config.operating_mode.title()} Mode - LIVE TRADING"
        })

        # Update API key status
        for i, (api_key, _) in enumerate(api_keys):
            await dashboard_q.put({
                "type": "api_status_update",
                "key": f"{api_key[:8]}...",
                "status": "ACTIVE"
            })

        logger.info("All agents started successfully!")
        logger.info("System is now running. Press Ctrl+C to stop.")

        # Keep the main loop running indefinitely
        await asyncio.gather(*tasks)

    except KeyboardInterrupt:
        logger.info("Shutdown signal received...")
    except Exception as e:
        logger.error(f"Fatal error in main: {e}")
        raise
    finally:
        logger.info("Shutting down agents...")
        # Cancel all tasks
        for task in tasks:
            if not task.done():
                task.cancel()
        logger.info("System shutdown complete.")

if __name__ == "__main__":
    # Create environment template if .env doesn't exist
    if not os.path.exists(".env"):
        from src.utils.config import create_env_template
        create_env_template()
        print("\n" + "="*60)
        print("FIRST TIME SETUP")
        print("="*60)
        print("A .env.template file has been created.")
        print("Please copy it to .env and fill in your Kraken API credentials:")
        print("  cp .env.template .env")
        print("  # Edit .env with your actual API keys")
        print("="*60)
        exit(1)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nSystem stopped by user.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        import traceback
        traceback.print_exc()


