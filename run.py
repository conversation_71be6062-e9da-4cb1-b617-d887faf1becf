#!/usr/bin/env python3
"""
Convenient runner script for Kraken Multi-Agent Trading System
"""

import argparse
import asyncio
import sys
from pathlib import Path

def print_banner():
    """Print system banner"""
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    KRAKEN MULTI-AGENT TRADING SYSTEM                        ║
║                           Advanced Crypto Trading Bot                        ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)

def check_setup():
    """Check if system is properly set up"""
    issues = []
    
    # Check if .env exists
    if not Path(".env").exists():
        issues.append("❌ .env file not found. Run 'python install.py' first.")
    
    # Check if src directory exists
    if not Path("src").exists():
        issues.append("❌ src directory not found. Check installation.")
    
    # Check if main.py exists
    if not Path("main.py").exists():
        issues.append("❌ main.py not found. Check installation.")
    
    return issues

def main():
    """Main runner function"""
    parser = argparse.ArgumentParser(
        description="Kraken Multi-Agent Trading System Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run.py                    # Start the trading system
  python run.py --test            # Run system tests
  python run.py --install         # Run installation
  python run.py --config          # Show current configuration
  python run.py --sandbox         # Force sandbox mode
  python run.py --live            # Force live trading mode (dangerous!)
        """
    )
    
    parser.add_argument(
        "--test", 
        action="store_true", 
        help="Run system tests instead of starting trading"
    )
    parser.add_argument(
        "--install", 
        action="store_true", 
        help="Run installation script"
    )
    parser.add_argument(
        "--config", 
        action="store_true", 
        help="Show current configuration"
    )
    parser.add_argument(
        "--sandbox", 
        action="store_true", 
        help="Force sandbox mode (paper trading)"
    )
    parser.add_argument(
        "--live", 
        action="store_true", 
        help="Force live trading mode (REAL MONEY AT RISK!)"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    print_banner()
    
    # Handle installation
    if args.install:
        print("Running installation script...")
        import subprocess
        result = subprocess.run([sys.executable, "install.py"])
        sys.exit(result.returncode)
    
    # Handle testing
    if args.test:
        print("Running system tests...")
        import subprocess
        result = subprocess.run([sys.executable, "test_system.py"])
        sys.exit(result.returncode)
    
    # Check setup
    issues = check_setup()
    if issues:
        print("Setup issues found:")
        for issue in issues:
            print(f"  {issue}")
        print("\nPlease run 'python run.py --install' to set up the system.")
        sys.exit(1)
    
    # Handle configuration display
    if args.config:
        try:
            from src.utils.config import load_config
            config = load_config()
            
            print("Current Configuration:")
            print("=" * 50)
            print(f"Operating Mode:     {config.operating_mode}")
            print(f"Sandbox Mode:       {config.use_sandbox}")
            print(f"Trading Pairs:      {', '.join(config.trading_pairs)}")
            print(f"Max Drawdown:       {config.max_portfolio_drawdown:.1%}")
            print(f"Max Position Size:  {config.max_position_size:.1%}")
            print(f"Log Level:          {config.log_level}")
            print(f"WebSocket URL:      {config.websocket_url}")
            print("=" * 50)
            
        except Exception as e:
            print(f"Error loading configuration: {e}")
            sys.exit(1)
        
        return
    
    # Handle mode overrides
    if args.sandbox and args.live:
        print("❌ Cannot specify both --sandbox and --live modes!")
        sys.exit(1)
    
    # Set environment variables for mode override
    if args.sandbox:
        import os
        os.environ["USE_SANDBOX"] = "True"
        print("🔒 Forced SANDBOX mode - Paper trading only")
    elif args.live:
        import os
        os.environ["USE_SANDBOX"] = "False"
        print("⚠️  Forced LIVE mode - REAL MONEY AT RISK!")
        
        # Extra confirmation for live mode
        response = input("Are you sure you want to trade with real money? (type 'YES' to confirm): ")
        if response != "YES":
            print("Live trading cancelled.")
            sys.exit(0)
    
    # Set verbose logging
    if args.verbose:
        import os
        os.environ["LOG_LEVEL"] = "DEBUG"
    
    # Start the trading system
    print("Starting Kraken Multi-Agent Trading System...")
    print("Press Ctrl+C to stop the system safely.")
    print("=" * 80)
    
    try:
        # Import and run main
        from main import main as trading_main
        asyncio.run(trading_main())
        
    except KeyboardInterrupt:
        print("\n" + "=" * 80)
        print("System stopped by user. Goodbye! 👋")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please check your installation and try again.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
