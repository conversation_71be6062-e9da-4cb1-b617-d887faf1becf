#!/usr/bin/env python3
"""
Test script for Kraken Multi-Agent Trading System
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent))

async def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        from src.agents.market_data_agent import MarketDataAgent
        from src.agents.strategy_signal_agent import StrategySignalAgent
        from src.agents.risk_execution_agent import RiskExecutionAgent
        from src.agents.dashboard_agent import DashboardAgent
        from src.utils.config import load_config
        from src.utils.logging_config import setup_logging
        
        print("✓ All modules imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

async def test_configuration():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from src.utils.config import load_config
        
        # Check if .env exists
        if not Path(".env").exists():
            print("⚠ .env file not found - using defaults")
        
        config = load_config()
        print(f"✓ Configuration loaded")
        print(f"  - Operating mode: {config.operating_mode}")
        print(f"  - Live trading: ENABLED")
        print(f"  - API keys: {len(config.kraken_api_keys)}")
        print(f"  - Trading pairs: {config.trading_pairs}")
        print(f"  - Max drawdown: {config.max_portfolio_drawdown}")
        
        return True
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

async def test_logging():
    """Test logging system"""
    print("\nTesting logging system...")
    
    try:
        from src.utils.logging_config import setup_logging, get_log_queue
        
        logger = setup_logging(log_level="INFO")
        log_queue = get_log_queue()
        
        # Test logging
        logger.info("Test log message")
        logger.warning("Test warning message")
        
        print("✓ Logging system working")
        return True
    except Exception as e:
        print(f"✗ Logging error: {e}")
        return False

async def test_market_data_agent():
    """Test Market Data Agent"""
    print("\nTesting Market Data Agent...")
    
    try:
        from src.agents.market_data_agent import MarketDataAgent
        
        # Create agent (don't connect to avoid network dependency)
        mda = MarketDataAgent("wss://test.example.com")
        
        # Test queue access
        queue = await mda.get_data_queue("ohlc")
        assert queue is not None
        
        print("✓ Market Data Agent initialized successfully")
        return True
    except Exception as e:
        print(f"✗ Market Data Agent error: {e}")
        return False

async def test_strategy_agent():
    """Test Strategy Signal Agent"""
    print("\nTesting Strategy Signal Agent...")
    
    try:
        from src.agents.strategy_signal_agent import StrategySignalAgent, TrendFollowingStrategy, ScalpingStrategy
        from src.agents.market_data_agent import MarketDataAgent
        
        # Create mock market data agent
        mda = MarketDataAgent("wss://test.example.com")
        
        # Test trend following strategy
        trend_strategy = TrendFollowingStrategy("XBT/USD", "240", 0.02)
        print("✓ Trend Following Strategy created")
        
        # Test scalping strategy
        scalping_strategy = ScalpingStrategy("ETH/USD", "1", 0.005)
        print("✓ Scalping Strategy created")
        
        # Test strategy signal agent
        ssa = StrategySignalAgent(mda, "trend_following", ["XBT/USD"])
        print("✓ Strategy Signal Agent initialized")
        
        return True
    except Exception as e:
        print(f"✗ Strategy Agent error: {e}")
        return False

async def test_risk_execution_agent():
    """Test Risk Execution Agent"""
    print("\nTesting Risk Execution Agent...")
    
    try:
        from src.agents.risk_execution_agent import RiskExecutionAgent, KrakenAPIClient
        
        # Create test queues
        signal_queue = asyncio.Queue()
        dashboard_queue = asyncio.Queue()
        
        # Test API client
        api_client = KrakenAPIClient("test_key", "test_secret")
        print("✓ Kraken API Client created")

        # Test risk execution agent
        rea = RiskExecutionAgent(
            signal_queue,
            dashboard_queue,
            [("test_key", "test_secret")],
            max_portfolio_drawdown=0.5
        )
        print("✓ Risk Execution Agent initialized")
        
        return True
    except Exception as e:
        print(f"✗ Risk Execution Agent error: {e}")
        return False

async def test_dashboard_agent():
    """Test Dashboard Agent"""
    print("\nTesting Dashboard Agent...")
    
    try:
        from src.agents.dashboard_agent import DashboardAgent
        from src.utils.logging_config import get_log_queue
        
        # Create test queues
        dashboard_queue = asyncio.Queue()
        log_queue = get_log_queue()
        
        # Test dashboard agent
        da = DashboardAgent(dashboard_queue, log_queue)
        print("✓ Dashboard Agent initialized")
        
        return True
    except Exception as e:
        print(f"✗ Dashboard Agent error: {e}")
        return False

async def test_integration():
    """Test basic integration between components"""
    print("\nTesting integration...")
    
    try:
        from src.agents.market_data_agent import MarketDataAgent
        from src.agents.strategy_signal_agent import StrategySignalAgent
        from src.agents.risk_execution_agent import RiskExecutionAgent
        from src.agents.dashboard_agent import DashboardAgent
        from src.utils.logging_config import get_log_queue
        from src.utils.config import load_config
        
        # Load config
        config = load_config()
        
        # Create queues
        signal_queue = asyncio.Queue()
        dashboard_queue = asyncio.Queue()
        
        # Create agents
        mda = MarketDataAgent(config.websocket_url)
        ssa = StrategySignalAgent(mda, config.operating_mode, config.trading_pairs)
        rea = RiskExecutionAgent(
            signal_queue,
            dashboard_queue,
            config.get_api_keys(),
            config.max_portfolio_drawdown
        )
        da = DashboardAgent(dashboard_queue, get_log_queue())
        
        print("✓ All agents created and connected successfully")
        return True
    except Exception as e:
        print(f"✗ Integration error: {e}")
        return False

async def main():
    """Run all tests"""
    print("="*60)
    print(" KRAKEN MULTI-AGENT TRADING SYSTEM - TEST SUITE")
    print("="*60)
    
    tests = [
        test_imports,
        test_configuration,
        test_logging,
        test_market_data_agent,
        test_strategy_agent,
        test_risk_execution_agent,
        test_dashboard_agent,
        test_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if await test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "="*60)
    print(" TEST RESULTS")
    print("="*60)
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total:  {passed + failed}")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! System is ready to run.")
        print("\nTo start the trading system, run: python main.py")
    else:
        print(f"\n❌ {failed} test(s) failed. Please check the errors above.")
        print("Make sure you have:")
        print("1. Installed all dependencies (pip install -r requirements.txt)")
        print("2. Created and configured the .env file")
        print("3. All required modules are in the correct locations")
    
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
