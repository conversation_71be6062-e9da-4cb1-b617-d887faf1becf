import asyncio
import json
import websockets
import logging
from collections import defaultdict

logging.basicConfig(level=logging.INFO, format=\'%(asctime)s - %(levelname)s - %(message)s\')

class MarketDataAgent:
    def __init__(self, websocket_url="wss://ws.kraken.com/"):
        self.websocket_url = websocket_url
        self.connection = None
        self.subscriptions = {}
        self.order_books = defaultdict(lambda: {\'bids\': {}, \'asks\': {}})
        self.ohlcv_data = defaultdict(list)
        self.data_queues = {
            "book": asyncio.Queue(),
            "ohlc": asyncio.Queue()
        } # Queues to stream data to other agents
        logging.info(f"MarketDataAgent initialized with WebSocket URL: {self.websocket_url}")

    async def connect(self):
        logging.info("Attempting to connect to Kraken WebSocket...")
        try:
            self.connection = await websockets.connect(self.websocket_url)
            logging.info("Successfully connected to Kraken WebSocket.")
            return True
        except Exception as e:
            logging.error(f"Failed to connect to WebSocket: {e}")
            return False

    async def disconnect(self):
        if self.connection:
            await self.connection.close()
            logging.info("Disconnected from Kraken WebSocket.")

    async def subscribe(self, channel, pair, interval=None):
        subscription_message = {
            "event": "subscribe",
            "pair": [pair],
            "subscription": {
                "name": channel
            }
        }
        if interval:
            subscription_message["subscription"]["interval"] = interval

        if self.connection:
            await self.connection.send(json.dumps(subscription_message))
            self.subscriptions[f"{channel}-{pair}-{interval}"] = subscription_message
            logging.info(f"Subscribed to {channel} for {pair} (interval: {interval}).")
        else:
            logging.warning("Not connected to WebSocket. Cannot subscribe.")

    async def parse_and_process_data(self, data):
        if isinstance(data, list) and len(data) > 1:
            channel_id = data[0]
            message_type = data[1]
            pair = data[-1]

            if message_type == \'book\':
                logging.info(f"Order Book update for {pair}: {data}")
                await self.data_queues["book"].put({"type": "book", "pair": pair, "data": data})

            elif message_type == \'ohlc\':
                ohlcv = data[1]
                logging.info(f"OHLCV data for {pair}: {ohlcv}")
                self.ohlcv_data[pair].append(ohlcv)
                await self.data_queues["ohlc"].put({"type": "ohlc", "pair": pair, "data": ohlcv})

            elif message_type == \'heartbeat\':
                logging.debug("Received heartbeat.")
            else:
                logging.info(f"Received unhandled message type: {message_type} for {pair}: {data}")
        elif isinstance(data, dict) and data.get("event") == "pong":
            logging.debug("Received pong.")
        elif isinstance(data, dict) and data.get("event") == "subscriptionStatus":
            logging.info(f"Subscription status: {data}")
        else:
            logging.info(f"Received unhandled message format: {data}")

    async def listen_for_data(self):
        if not self.connection:
            logging.error("Not connected to WebSocket. Cannot listen for data.")
            return

        try:
            async for message in self.connection:
                data = json.loads(message)
                await self.parse_and_process_data(data)
        except websockets.exceptions.ConnectionClosedOK:
            logging.info("WebSocket connection closed gracefully.")
        except Exception as e:
            logging.error(f"Error while listening for data: {e}")

    async def run(self):
        while True:
            if not self.connection or not self.connection.open:
                if not await self.connect():
                    await asyncio.sleep(5) # Wait before retrying connection
                    continue
                for sub_key, sub_msg in self.subscriptions.items():
                    logging.info(f"Re-subscribing to {sub_key}...")
                    await self.connection.send(json.dumps(sub_msg))

            await self.listen_for_data()
            await asyncio.sleep(1) # Prevent busy-waiting

    async def get_data_queue(self, data_type):
        if data_type in self.data_queues:
            return self.data_queues[data_type]
        else:
            raise ValueError(f"Unknown data type: {data_type}")

async def main():
    mda = MarketDataAgent()
    await mda.connect()
    await mda.subscribe("book", "XBT/USD")
    await mda.subscribe("ohlc", "XBT/USD", interval=1)

    # Example of another agent consuming data
    async def data_consumer(queue, name):
        while True:
            data = await queue.get()
            logging.info(f"Consumer {name} received: {data}")

    # Start the MDA and a consumer concurrently
    await asyncio.gather(
        mda.run(),
        data_consumer(mda.data_queues["book"], "Book Consumer"),
        data_consumer(mda.data_queues["ohlc"], "OHLCV Consumer")
    )

if __name__ == "__main__":
    asyncio.run(main())


