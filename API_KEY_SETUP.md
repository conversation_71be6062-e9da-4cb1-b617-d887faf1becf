# 🔑 API Key Setup Guide - Automatic Configuration

## ✅ **YES! The batch file automatically reads your API keys from the .env file!**

You only need to configure your API keys **ONCE**, and the system will automatically use them every time you run the batch file.

## 🚀 **How It Works**

### 1. **One-Time Setup**
- Configure your 5 API keys once using the trading configurator
- The keys are saved in your `.env` file
- The batch file automatically reads them every time

### 2. **Automatic Detection**
- The batch file checks if your `.env` file exists
- It validates that all 5 API keys are properly configured
- If anything is missing, it offers to run the configurator

### 3. **No Manual Entry Required**
- After initial setup, no need to enter API keys again
- The system automatically loads your configuration
- Just run the batch file and start trading!

## 📋 **Setup Process**

### **Step 1: Initial Configuration**
```batch
# Run the batch file
launch_trading_system.bat

# Choose option 2: Configure Trading System
# Enter your 5 API keys once
# Save the configuration
```

### **Step 2: Automatic Usage**
```batch
# Every subsequent run:
launch_trading_system.bat

# Choose option 3: Launch Live Trading System
# Your API keys are automatically loaded!
```

## 🔧 **Configuration Options**

### **Option 1: Quick Setup Check**
- View your current configuration
- See which API keys are configured
- Check all trading settings
- **No changes made** - just displays info

### **Option 2: Configure Trading System**
- **Detects existing API keys** and shows them
- Asks if you want to keep existing keys or enter new ones
- Updates only the settings you want to change
- **Remembers your previous configuration**

### **Option 3: Launch Live Trading**
- **Automatically reads** your API keys from `.env`
- Validates configuration before starting
- No manual input required!

## 📁 **File Structure**

```
Your Trading Directory/
├── .env                    # Your API keys (auto-read by batch file)
├── launch_trading_system.bat  # Main launcher (reads .env automatically)
├── trading_configurator.py    # One-time setup tool
├── quick_setup.py             # View current config
└── check_config.py            # Validates .env file
```

## 🔒 **Your .env File Format**

Once configured, your `.env` file looks like this:
```bash
# Your 5 API keys (automatically read by the system)
KRAKEN_API_KEY_1=your_first_api_key_here
KRAKEN_API_SECRET_1=your_first_api_secret_here
KRAKEN_API_KEY_2=your_second_api_key_here
KRAKEN_API_SECRET_2=your_second_api_secret_here
# ... (keys 3, 4, 5)

# Trading settings (also auto-loaded)
OPERATING_MODE=trend_following
TRADING_PAIRS=XBT/USD,ETH/USD
MAX_PORTFOLIO_DRAWDOWN=0.50
# ... (other settings)
```

## ⚡ **Smart Features**

### **Automatic Validation**
- Batch file checks if all 5 API keys are present
- Validates configuration before starting trading
- Offers to run configurator if anything is missing

### **Configuration Memory**
- Trading configurator remembers your previous settings
- Shows existing API keys (masked for security)
- Only asks for changes you want to make

### **Error Recovery**
- If `.env` file is missing, offers to create it
- If API keys are incomplete, guides you to fix them
- Smart error messages with clear solutions

## 🎯 **Quick Start Workflow**

### **First Time:**
1. Run `launch_trading_system.bat`
2. Choose option 2 (Configure Trading System)
3. Enter your 5 API keys and settings
4. Save configuration

### **Every Time After:**
1. Run `launch_trading_system.bat`
2. Choose option 3 (Launch Live Trading)
3. **API keys automatically loaded!**
4. Start trading immediately

## 🛠️ **Troubleshooting**

### **"Configuration check failed"**
- Your `.env` file is missing or incomplete
- Choose option 2 to configure your API keys
- Or manually edit the `.env` file

### **"Only X/5 API keys configured"**
- Some API key slots are empty
- Run the configurator to add missing keys
- Check your `.env` file for typos

### **"API key seems too short"**
- Check you copied the full API key from Kraken
- Make sure no spaces or extra characters
- Verify the key has proper permissions

## 🔐 **Security Notes**

- **Never share your .env file** - it contains your API keys
- **Keep backups secure** - treat like passwords
- **Check file permissions** - make sure only you can read it
- **Regular rotation** - consider rotating API keys periodically

## 📞 **Need Help?**

### **Check Current Configuration:**
```batch
# Option 1: Quick Setup Check
# Shows all your current settings without making changes
```

### **Update Configuration:**
```batch
# Option 2: Configure Trading System  
# Remembers existing settings, only change what you want
```

### **Manual Check:**
```bash
python quick_setup.py    # View detailed configuration
python check_config.py   # Validate .env file
```

---

## 🎉 **Summary**

✅ **Configure once** - API keys saved in `.env` file  
✅ **Automatic loading** - Batch file reads keys every time  
✅ **No manual entry** - Just run and trade  
✅ **Smart validation** - Checks configuration before starting  
✅ **Easy updates** - Configurator remembers your settings  

**Your API keys are automatically loaded every time you run the batch file!** 🚀
