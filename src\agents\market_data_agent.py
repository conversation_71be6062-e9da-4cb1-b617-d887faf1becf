import asyncio
import json
import websockets
import logging
from collections import defaultdict
from typing import Dict, Optional, Any

logger = logging.getLogger(__name__)

class MarketDataAgent:
    """
    Market Data Agent for handling real-time market data from Kraken WebSocket API.
    
    Responsibilities:
    - Establish and maintain WebSocket connection to Kraken
    - Subscribe to market data channels (order book, OHLC)
    - Parse and distribute market data to other agents via queues
    - Handle connection failures and automatic reconnection
    """
    
    def __init__(self, websocket_url: str = "wss://ws.kraken.com/"):
        self.websocket_url = websocket_url
        self.connection: Optional[websockets.WebSocketServerProtocol] = None
        self.subscriptions: Dict[str, Dict] = {}
        self.order_books = defaultdict(lambda: {'bids': {}, 'asks': {}})
        self.ohlcv_data = defaultdict(list)
        self.data_queues: Dict[str, asyncio.Queue] = {
            "book": asyncio.Queue(),
            "ohlc": asyncio.Queue()
        }
        self.is_running = False
        self.reconnect_delay = 5  # seconds
        self.max_reconnect_attempts = 10
        
        logger.info(f"MarketDataAgent initialized with WebSocket URL: {self.websocket_url}")

    async def connect(self) -> bool:
        """Establish WebSocket connection to Kraken"""
        logger.info("Attempting to connect to Kraken WebSocket...")
        try:
            self.connection = await websockets.connect(
                self.websocket_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            )
            logger.info("Successfully connected to Kraken WebSocket.")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to WebSocket: {e}")
            return False

    async def disconnect(self):
        """Close WebSocket connection"""
        if self.connection:
            await self.connection.close()
            logger.info("Disconnected from Kraken WebSocket.")
            self.connection = None

    async def subscribe(self, channel: str, pair: str, interval: Optional[int] = None):
        """
        Subscribe to a market data channel
        
        Args:
            channel: Channel name ('book', 'ohlc', 'ticker', etc.)
            pair: Trading pair (e.g., 'XBT/USD')
            interval: OHLC interval in minutes (for ohlc channel)
        """
        subscription_message = {
            "event": "subscribe",
            "pair": [pair],
            "subscription": {
                "name": channel
            }
        }
        
        if interval and channel == "ohlc":
            subscription_message["subscription"]["interval"] = interval

        subscription_key = f"{channel}-{pair}-{interval}"
        
        if self.connection and not self.connection.closed:
            try:
                await self.connection.send(json.dumps(subscription_message))
                self.subscriptions[subscription_key] = subscription_message
                logger.info(f"Subscribed to {channel} for {pair} (interval: {interval})")
            except Exception as e:
                logger.error(f"Failed to subscribe to {subscription_key}: {e}")
        else:
            logger.warning("Not connected to WebSocket. Cannot subscribe.")
            # Store subscription for later when connection is established
            self.subscriptions[subscription_key] = subscription_message

    async def unsubscribe(self, channel: str, pair: str, interval: Optional[int] = None):
        """Unsubscribe from a market data channel"""
        unsubscription_message = {
            "event": "unsubscribe",
            "pair": [pair],
            "subscription": {
                "name": channel
            }
        }
        
        if interval and channel == "ohlc":
            unsubscription_message["subscription"]["interval"] = interval

        subscription_key = f"{channel}-{pair}-{interval}"
        
        if self.connection and not self.connection.closed:
            try:
                await self.connection.send(json.dumps(unsubscription_message))
                self.subscriptions.pop(subscription_key, None)
                logger.info(f"Unsubscribed from {channel} for {pair} (interval: {interval})")
            except Exception as e:
                logger.error(f"Failed to unsubscribe from {subscription_key}: {e}")

    async def parse_and_process_data(self, data: Any):
        """Parse incoming WebSocket data and distribute to appropriate queues"""
        try:
            if isinstance(data, list) and len(data) > 1:
                channel_id = data[0]
                message_type = data[1] if len(data) > 1 else None
                pair = data[-1] if len(data) > 2 else None

                if message_type == 'book':
                    logger.debug(f"Order Book update for {pair}")
                    await self.data_queues["book"].put({
                        "type": "book", 
                        "pair": pair, 
                        "data": data,
                        "timestamp": asyncio.get_event_loop().time()
                    })

                elif message_type == 'ohlc':
                    ohlcv = data[1]
                    logger.debug(f"OHLCV data for {pair}")
                    self.ohlcv_data[pair].append(ohlcv)
                    await self.data_queues["ohlc"].put({
                        "type": "ohlc", 
                        "pair": pair, 
                        "data": ohlcv,
                        "timestamp": asyncio.get_event_loop().time()
                    })

                elif message_type == 'heartbeat':
                    logger.debug("Received heartbeat")
                else:
                    logger.debug(f"Received unhandled message type: {message_type} for {pair}")
                    
            elif isinstance(data, dict):
                event = data.get("event")
                if event == "pong":
                    logger.debug("Received pong")
                elif event == "subscriptionStatus":
                    status = data.get("status")
                    if status == "subscribed":
                        logger.info(f"Subscription confirmed: {data}")
                    elif status == "error":
                        logger.error(f"Subscription error: {data}")
                elif event == "systemStatus":
                    logger.info(f"System status: {data}")
                else:
                    logger.debug(f"Received unhandled event: {event}")
            else:
                logger.debug(f"Received unhandled message format: {data}")
                
        except Exception as e:
            logger.error(f"Error processing data: {e}, Data: {data}")

    async def listen_for_data(self):
        """Listen for incoming WebSocket messages"""
        if not self.connection:
            logger.error("Not connected to WebSocket. Cannot listen for data.")
            return

        try:
            async for message in self.connection:
                try:
                    data = json.loads(message)
                    await self.parse_and_process_data(data)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON message: {e}, Message: {message}")
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    
        except websockets.exceptions.ConnectionClosedOK:
            logger.info("WebSocket connection closed gracefully")
        except websockets.exceptions.ConnectionClosedError as e:
            logger.warning(f"WebSocket connection closed with error: {e}")
        except Exception as e:
            logger.error(f"Error while listening for data: {e}")

    async def resubscribe_all(self):
        """Resubscribe to all previously subscribed channels"""
        for sub_key, sub_msg in self.subscriptions.items():
            try:
                logger.info(f"Re-subscribing to {sub_key}...")
                await self.connection.send(json.dumps(sub_msg))
                await asyncio.sleep(0.1)  # Small delay between subscriptions
            except Exception as e:
                logger.error(f"Failed to re-subscribe to {sub_key}: {e}")

    async def run(self):
        """Main run loop with automatic reconnection"""
        self.is_running = True
        reconnect_attempts = 0
        
        logger.info("MarketDataAgent starting...")
        
        while self.is_running:
            try:
                # Connect if not connected
                if not self.connection or self.connection.closed:
                    if await self.connect():
                        reconnect_attempts = 0
                        # Resubscribe to all channels
                        if self.subscriptions:
                            await self.resubscribe_all()
                    else:
                        reconnect_attempts += 1
                        if reconnect_attempts >= self.max_reconnect_attempts:
                            logger.error(f"Max reconnection attempts ({self.max_reconnect_attempts}) reached. Stopping.")
                            break
                        
                        delay = min(self.reconnect_delay * reconnect_attempts, 60)  # Max 60 seconds
                        logger.warning(f"Reconnection attempt {reconnect_attempts}/{self.max_reconnect_attempts} failed. "
                                     f"Retrying in {delay} seconds...")
                        await asyncio.sleep(delay)
                        continue

                # Listen for data
                await self.listen_for_data()
                
            except Exception as e:
                logger.error(f"Unexpected error in MarketDataAgent run loop: {e}")
                await asyncio.sleep(self.reconnect_delay)

        logger.info("MarketDataAgent stopped")

    async def stop(self):
        """Stop the market data agent"""
        logger.info("Stopping MarketDataAgent...")
        self.is_running = False
        await self.disconnect()

    async def get_data_queue(self, data_type: str) -> asyncio.Queue:
        """Get a data queue for a specific data type"""
        if data_type in self.data_queues:
            return self.data_queues[data_type]
        else:
            raise ValueError(f"Unknown data type: {data_type}. Available types: {list(self.data_queues.keys())}")

    def get_order_book(self, pair: str) -> Dict:
        """Get the current order book for a trading pair"""
        return self.order_books.get(pair, {'bids': {}, 'asks': {}})

    def get_ohlcv_data(self, pair: str) -> list:
        """Get OHLCV data for a trading pair"""
        return self.ohlcv_data.get(pair, [])

# Example usage and testing
async def main():
    """Example usage of MarketDataAgent"""
    mda = MarketDataAgent()
    
    # Start the agent
    agent_task = asyncio.create_task(mda.run())
    
    # Wait a bit for connection
    await asyncio.sleep(2)
    
    # Subscribe to data
    await mda.subscribe("book", "XBT/USD")
    await mda.subscribe("ohlc", "XBT/USD", interval=1)

    # Example consumer
    async def data_consumer(queue: asyncio.Queue, name: str):
        while True:
            try:
                data = await asyncio.wait_for(queue.get(), timeout=1.0)
                logger.info(f"Consumer {name} received: {data['type']} for {data['pair']}")
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Consumer {name} error: {e}")

    # Start consumers
    book_consumer = asyncio.create_task(
        data_consumer(mda.data_queues["book"], "Book Consumer")
    )
    ohlc_consumer = asyncio.create_task(
        data_consumer(mda.data_queues["ohlc"], "OHLC Consumer")
    )

    # Run for a while then stop
    try:
        await asyncio.sleep(30)  # Run for 30 seconds
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    finally:
        await mda.stop()
        agent_task.cancel()
        book_consumer.cancel()
        ohlc_consumer.cancel()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    asyncio.run(main())
