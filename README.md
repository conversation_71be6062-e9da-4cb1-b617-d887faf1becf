# Kraken Multi-Agent Live Trading System

A sophisticated, modular cryptocurrency trading system designed for **LIVE TRADING** on the Kraken exchange. This system employs a multi-agent architecture for high-frequency, risk-managed algorithmic trading with comprehensive monitoring and safety features.

## ⚠️ **LIVE TRADING ONLY - REAL MONEY AT RISK** ⚠️

This system is configured for **LIVE TRADING ONLY** using real Kraken API keys and real money. There is no sandbox or paper trading mode.

## 🚀 Features

### Core Architecture
- **Multi-Agent Design**: Four specialized agents working in concert
- **Asynchronous Processing**: High-performance async/await implementation
- **Modular Structure**: Easy to extend and customize
- **Comprehensive Logging**: Detailed system monitoring and debugging

### Trading Capabilities
- **Multiple Strategies**: Trend-following and scalping algorithms
- **Risk Management**: Portfolio drawdown limits, position sizing, volatility halts
- **API Management**: Intelligent rotation of 5 Kraken API keys
- **Real-time Monitoring**: Live dashboard with performance metrics

### Safety Features
- **Circuit Breakers**: Automatic trading halts on excessive losses
- **Error Recovery**: Robust error handling and automatic reconnection
- **Configuration Validation**: Comprehensive parameter checking
- **Multi-Key Rotation**: Intelligent API key management for reliability

## 🏗️ System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Market Data    │───▶│  Strategy &      │───▶│  Risk &         │
│  Agent          │    │  Signal Agent    │    │  Execution      │
│                 │    │                  │    │  Agent          │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Dashboard Agent                              │
│              (Real-time Monitoring & Control)                  │
└─────────────────────────────────────────────────────────────────┘
```

### Agent Responsibilities

#### 🔄 Market Data Agent
- Real-time WebSocket connection to Kraken
- Order book and OHLC data ingestion
- Data distribution to strategy agents
- Connection management and auto-reconnection

#### 📊 Strategy & Signal Agent
- Technical analysis and signal generation
- Multiple trading strategies (trend-following, scalping)
- Risk-adjusted position sizing
- Signal filtering and confirmation

#### ⚖️ Risk & Execution Agent
- Order placement and management
- Portfolio risk monitoring
- API key rotation and rate limiting
- Emergency halt mechanisms

#### 📱 Dashboard Agent
- Real-time system monitoring
- Performance metrics display
- Alert management
- System status visualization

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8 or higher
- pip package manager
- Kraken account with **5 API keys** (with trading permissions)
- **REAL MONEY** for live trading

### Quick Setup (Windows) - **API Keys Auto-Loaded!**
```batch
# Run the batch file - it automatically reads your API keys from .env
launch_trading_system.bat
```

**The batch file automatically reads your API keys from the .env file!** You only need to configure them once.

### Manual Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Configure your 5 API keys (one-time setup)
python trading_configurator.py

# Test the system
python test_system.py

# Start live trading (API keys auto-loaded)
python main.py
```

### API Key Setup (One-Time Configuration)
You need **5 Kraken API keys** with trading permissions:
1. Go to https://www.kraken.com/u/security/api
2. Create 5 API keys with trading permissions
3. Use the trading configurator to set them up **once**
4. **The system automatically reads them from .env every time!**
5. **Keep your keys secure and never share them**

## ⚙️ Configuration

### Using the Trading Configurator
The easiest way to configure the system:

```bash
python trading_configurator.py
```

This interactive tool will guide you through:
- Setting up your 5 Kraken API keys
- Configuring trading parameters
- Setting risk management rules
- Choosing system settings

### Manual Configuration
Edit the `.env` file with your settings:

```bash
# API Configuration (5 keys required)
KRAKEN_API_KEY_1=your_first_api_key
KRAKEN_API_SECRET_1=your_first_api_secret
KRAKEN_API_KEY_2=your_second_api_key
KRAKEN_API_SECRET_2=your_second_api_secret
# ... (keys 3, 4, 5)

# Trading Parameters
OPERATING_MODE=trend_following  # or "scalping"
TRADING_PAIRS=XBT/USD,ETH/USD
MAX_PORTFOLIO_DRAWDOWN=0.50
INITIAL_PORTFOLIO_VALUE=10000.0

# Risk Management
MAX_POSITION_SIZE=0.1
STOP_LOSS_PERCENTAGE=0.02
TAKE_PROFIT_PERCENTAGE=0.04
```

### Trading Modes

#### Trend Following
- **Timeframe**: 4-hour candles
- **Indicators**: EMA crossover + RSI confirmation
- **Risk**: 2% per trade
- **Best for**: Medium to long-term trends

#### Scalping
- **Timeframe**: 1-minute candles
- **Indicators**: Bollinger Bands + volume analysis
- **Risk**: 0.5% per trade
- **Best for**: Short-term price movements

## 🚀 Usage

### Quick Start (Windows) - **Automatic API Key Loading!**
```batch
# The launcher automatically reads your API keys from .env
launch_trading_system.bat

# Menu options:
# 1. Quick Setup Check (view current config)
# 2. Configure Trading System (one-time setup)
# 3. Launch Live Trading (API keys auto-loaded!)
# 4. Launch Dashboard Only
# 5. Run System Tests
```

### Manual Start
```bash
# Configure the system (one-time setup)
python trading_configurator.py

# Check your configuration
python quick_setup.py

# Test the installation
python test_system.py

# Start live trading (API keys automatically loaded)
python main.py

# Or launch dashboard only
python launch_dashboard.py
```

### Dashboard Interface
The system provides a real-time CLI dashboard showing:
- Portfolio performance and equity
- Open positions and recent trades
- API key status and rotation
- System logs and alerts
- Trading statistics

### ⚠️ Safety Warnings
1. **This system trades with REAL MONEY**
2. **Start with small amounts**
3. **Monitor the system** regularly
4. **Understand the risks** before trading
5. **Never invest more than you can afford to lose**

## 📊 Monitoring & Alerts

### Real-time Metrics
- Total equity and P&L
- Current drawdown vs. limits
- Win rate and trade statistics
- API performance and status

### Alert System
- Portfolio drawdown warnings
- Volatility halt notifications
- API key rotation alerts
- System error notifications

### Logging
- Comprehensive log files in `logs/` directory
- Configurable log levels
- Automatic log rotation
- Real-time log streaming to dashboard

## 🔒 Risk Management

### Portfolio Protection
- **Maximum Drawdown**: Configurable portfolio loss limit
- **Position Sizing**: Risk-based position calculation
- **Volatility Halts**: Trading pause on excessive price movements
- **Circuit Breakers**: Emergency stop on critical losses

### API Management
- **Key Rotation**: Automatic switching between API keys
- **Rate Limiting**: Intelligent request throttling
- **Error Recovery**: Automatic retry with exponential backoff
- **Quarantine System**: Temporary key suspension on errors

## 🧪 Testing

### Test Suite
```bash
# Run comprehensive tests
python test_system.py

# Test individual components
python -m pytest tests/
```

### Backtesting
```bash
# Run historical backtests
python backtesting_engine.py
```

## 📁 Project Structure

```
CryptoTrade/
├── src/
│   ├── agents/
│   │   ├── market_data_agent.py
│   │   ├── strategy_signal_agent.py
│   │   ├── risk_execution_agent.py
│   │   └── dashboard_agent.py
│   └── utils/
│       ├── config.py
│       └── logging_config.py
├── logs/                    # Log files
├── data/                    # Market data cache
├── tests/                   # Test files
├── main.py                  # Main entry point
├── install.py              # Installation script
├── test_system.py          # Test suite
├── requirements.txt        # Dependencies
├── .env.template          # Configuration template
└── README.md              # This file
```

## 🔧 Development

### Adding New Strategies
1. Create a new strategy class inheriting from `BaseStrategy`
2. Implement required methods: `on_ohlcv_update`, `on_order_book_update`, etc.
3. Add strategy to `StrategySignalAgent` configuration
4. Test thoroughly in sandbox mode

### Extending Functionality
- **New Indicators**: Add to strategy classes
- **Additional Exchanges**: Implement new API clients
- **Enhanced Risk Rules**: Extend `RiskExecutionAgent`
- **Custom Dashboards**: Modify `DashboardAgent`

### Code Quality
```bash
# Format code
black src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/
```

## 📈 Performance Optimization

### System Tuning
- Adjust queue sizes for high-frequency trading
- Optimize WebSocket connection parameters
- Configure logging levels for production
- Tune API rate limits based on your tier

### Memory Management
- Regular cleanup of old market data
- Log file rotation
- Efficient data structures for large datasets

## 🚨 Troubleshooting

### Common Issues

#### Connection Problems
```bash
# Check network connectivity
ping api.kraken.com

# Verify WebSocket access
telnet ws.kraken.com 443
```

#### API Key Issues
- Verify key permissions in Kraken account
- Check key expiration dates
- Ensure proper sandbox/production key usage

#### Configuration Errors
```bash
# Validate configuration
python -c "from src.utils.config import load_config; load_config()"

# Check environment variables
python -c "import os; print(os.getenv('USE_SANDBOX'))"
```

### Debug Mode
```bash
# Run with debug logging
LOG_LEVEL=DEBUG python main.py

# Enable verbose output
python main.py --verbose
```

## 📋 Deployment

### Production Checklist
- [ ] API keys configured and tested
- [ ] Risk parameters reviewed and approved
- [ ] Backup and recovery procedures in place
- [ ] Monitoring and alerting configured
- [ ] Emergency stop procedures documented

### Server Deployment
```bash
# Using systemd (Linux)
sudo cp kraken-trading.service /etc/systemd/system/
sudo systemctl enable kraken-trading
sudo systemctl start kraken-trading

# Using Docker
docker build -t kraken-trading .
docker run -d --name trading-bot kraken-trading
```

### Monitoring in Production
- Set up external monitoring (e.g., Prometheus, Grafana)
- Configure email/SMS alerts for critical events
- Regular backup of configuration and logs
- Performance monitoring and optimization

## 🤝 Contributing

### Development Setup
```bash
# Install development dependencies
pip install -r requirements.txt
pip install -e .[dev]

# Set up pre-commit hooks
pre-commit install

# Run tests
pytest tests/
```

### Contribution Guidelines
1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

**IMPORTANT**: This software is for educational and research purposes. Cryptocurrency trading involves substantial risk of loss. The authors are not responsible for any financial losses incurred through the use of this software.

### Risk Warnings
- **Past performance does not guarantee future results**
- **Start with small amounts and paper trading**
- **Never invest more than you can afford to lose**
- **Understand the risks before trading with real money**
- **This software is provided "as is" without warranty**

## 📞 Support

### Getting Help
- **Documentation**: Check this README and code comments
- **Issues**: Report bugs via GitHub issues
- **Discussions**: Join community discussions
- **Email**: Contact maintainers for critical issues

### Community
- GitHub Discussions for general questions
- Discord server for real-time chat
- Reddit community for strategy discussions

## 🗺️ Roadmap

### Version 2.0 (Planned)
- [ ] Web-based dashboard interface
- [ ] Machine learning strategy optimization
- [ ] Multi-exchange support
- [ ] Advanced backtesting framework
- [ ] Portfolio optimization algorithms

### Version 3.0 (Future)
- [ ] Cloud deployment options
- [ ] Mobile app for monitoring
- [ ] Social trading features
- [ ] Advanced analytics and reporting
- [ ] Integration with external data sources

## 📚 Additional Resources

### Learning Materials
- [Kraken API Documentation](https://docs.kraken.com/rest/)
- [Algorithmic Trading Basics](https://www.investopedia.com/articles/active-trading/101014/basics-algorithmic-trading-concepts-and-examples.asp)
- [Risk Management in Trading](https://www.investopedia.com/articles/trading/09/risk-management.asp)

### Related Projects
- [ccxt](https://github.com/ccxt/ccxt) - Cryptocurrency exchange library
- [freqtrade](https://github.com/freqtrade/freqtrade) - Algorithmic trading bot
- [zipline](https://github.com/quantopian/zipline) - Backtesting library

---

**Happy Trading! 🚀📈**

*Remember: The best strategy is the one you understand and can stick to consistently.*


