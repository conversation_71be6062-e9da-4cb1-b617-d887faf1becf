# Kraken Multi-Agent Cryptocurrency Trading System

This project aims to develop a modular, multi-agent cryptocurrency trading system for the Kraken exchange. The system is designed for high-frequency, risk-managed algorithmic trading with user-selectable operational modes and a resilient API management framework.

## Core Components:

- **Market Data Agent**: Handles real-time market data ingestion from Kraken WebSockets.
- **Strategy & Signal Agent**: Generates trading signals based on selected operating modes (Trend-Following or Scalping).
- **Risk & Execution Agent**: Manages order placement, risk checks, and intelligent API key rotation.
- **Dashboard Agent**: Provides a real-time command-line interface for system monitoring.

## Key Features:

- Multi-agent architecture for concurrent operation.
- User-selectable trading modes with distinct strategies and risk profiles.
- Robust risk management protocols, including master circuit breaker and volatility halt.
- Intelligent API key management with rate-limit awareness.
- Real-time CLI dashboard for comprehensive system visibility.

## Phased Implementation:

1. **Backtesting**: Rigorous testing on historical data.
2. **Paper Trading**: Deployment in a simulated environment with real Kraken API sandbox.
3. **Live Deployment**: Live trading with capital after successful completion of prior phases.


