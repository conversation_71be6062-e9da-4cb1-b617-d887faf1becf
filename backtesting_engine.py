import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime

# Assuming these are available from other files
from src.agents.strategy_signal_agent import BaseStrategy, TrendFollowingStrategy, ScalpingStrategy
from src.backtesting.historical_data_loader import HistoricalDataLoader

logging.basicConfig(level=logging.INFO, format=\'%(asctime)s - %(levelname)s - %(message)s\')

class BacktestingEngine:
    def __init__(self, initial_capital: float, commission_rate: float = 0.002, slippage_percent: float = 0.0005):
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage_percent = slippage_percent
        self.portfolio_value = initial_capital
        self.cash = initial_capital
        self.positions = defaultdict(float) # {pair: volume}
        self.trade_history = []
        self.equity_curve = [] # (timestamp, equity_value)
        self.current_timestamp = None
        logging.info(f"BacktestingEngine initialized with initial capital: {initial_capital}")

    async def run_backtest(self, strategy: BaseStrategy, ohlcv_data: pd.DataFrame):
        logging.info(f"Starting backtest for strategy: {strategy.__class__.__name__} on {strategy.pair} ({strategy.timeframe})")

        # Reset for each backtest run
        self.portfolio_value = self.initial_capital
        self.cash = self.initial_capital
        self.positions = defaultdict(float)
        self.trade_history = []
        self.equity_curve = []

        # Simulate real-time data feed
        for index, row in ohlcv_data.iterrows():
            self.current_timestamp = index
            ohlcv_dict = {
                0: index.timestamp(), # Timestamp
                1: str(row["open"]),
                2: str(row["high"]),
                3: str(row["low"]),
                4: str(row["close"]),
                5: str(row["vwap"]),
                6: str(row["volume"]),
                7: str(row["count"])
            }

            # Update strategy with new OHLCV data
            await strategy.on_ohlcv_update(ohlcv_dict)

            # Check for signals from the strategy
            signals = strategy.get_signals()
            if signals:
                for signal in signals:
                    await self._execute_trade(signal)
                strategy.clear_signals()

            # Record equity curve at each step (using close price for simplicity)
            current_price = float(ohlcv_dict[4])
            current_portfolio_value = self.cash
            for pair, volume in self.positions.items():
                # This assumes current_price is for the strategy.pair. Needs refinement for multi-pair.
                current_portfolio_value += volume * current_price
            self.equity_curve.append((self.current_timestamp, current_portfolio_value))

        logging.info("Backtest completed.")
        self._analyze_results()

    async def _execute_trade(self, signal: dict):
        trade_type = signal["type"]
        pair = signal["pair"]
        price = signal["price"]
        risk_percentage = signal["risk_percentage"]

        if trade_type == "entry":
            # Calculate position size based on risk
            # Simplified: assume fixed amount for now, real calculation needs stop loss
            # For backtesting, we can assume we risk the percentage of current equity
            risk_amount = self.portfolio_value * risk_percentage
            # Assuming 1 unit of volume for every $1000 risked for simplicity
            # In a real backtest, this would be based on entry price and stop loss
            volume_to_trade = risk_amount / 1000.0 # Dummy calculation

            if self.cash >= volume_to_trade * price: # Check if enough cash
                # Apply slippage
                execution_price = price * (1 + self.slippage_percent)
                cost = volume_to_trade * execution_price
                commission = cost * self.commission_rate

                self.cash -= (cost + commission)
                self.positions[pair] += volume_to_trade
                self.portfolio_value = self.cash + self.positions[pair] * execution_price

                self.trade_history.append({
                    "timestamp": self.current_timestamp,
                    "pair": pair,
                    "type": "BUY",
                    "volume": volume_to_trade,
                    "price": execution_price,
                    "commission": commission,
                    "pnl": 0 # PnL is calculated on close
                })
                logging.info(f"Executed BUY for {volume_to_trade:.5f} {pair} at {execution_price:.2f}")
            else:
                logging.warning(f"Insufficient cash to execute trade for {pair}. Signal rejected.")
        # TODO: Implement take_profit and stop_loss logic for closing positions

    def _analyze_results(self):
        if not self.equity_curve:
            logging.warning("No equity curve data to analyze.")
            return

        equity_df = pd.DataFrame(self.equity_curve, columns=["timestamp", "equity"])
        equity_df.set_index("timestamp", inplace=True)

        # Calculate basic metrics
        total_return = (equity_df["equity"].iloc[-1] / equity_df["equity"].iloc[0]) - 1
        max_drawdown = (equity_df["equity"] / equity_df["equity"].cummax() - 1).min()

        logging.info("\n--- Backtest Results ---")
        logging.info(f"Initial Capital: {self.initial_capital:.2f}")
        logging.info(f"Final Equity: {self.portfolio_value:.2f}")
        logging.info(f"Total Return: {total_return:.2%}")
        logging.info(f"Max Drawdown: {max_drawdown:.2%}")
        logging.info("------------------------")

        # You can add more sophisticated metrics and plotting here

async def main():
    loader = HistoricalDataLoader(data_dir="./data/kraken_historical/")
    # For demonstration, we will create a dummy CSV file if it doesn't exist
    dummy_data_path = "./data/kraken_historical/XBTUSD_1min.csv"
    if not os.path.exists(dummy_data_path):
        logging.info(f"Creating dummy historical data file: {dummy_data_path}")
        # Create 1000 data points for XBT/USD 1-minute OHLCV
        data = []
        start_ts = int(datetime(2024, 1, 1).timestamp())
        price = 40000.0
        for i in range(1000):
            ts = start_ts + i * 60 # 1 minute interval
            open_p = price
            close_p = price + np.random.uniform(-10, 10)
            high_p = max(open_p, close_p) + np.random.uniform(0, 5)
            low_p = min(open_p, close_p) - np.random.uniform(0, 5)
            volume = np.random.uniform(1, 10)
            vwap = (open_p + high_p + low_p + close_p) / 4
            count = np.random.randint(100, 500)
            data.append([ts, open_p, high_p, low_p, close_p, vwap, volume, count])
            price = close_p # Next open is current close

        dummy_df = pd.DataFrame(data, columns=["time", "open", "high", "low", "close", "vwap", "volume", "count"])
        # Add a dummy header row as per Kraken's CSV format
        with open(dummy_data_path, 'w') as f:
            f.write("OHLCVT\n")
        dummy_df.to_csv(dummy_data_path, mode='a', index=False, header=False)
        logging.info("Dummy historical data created.")

    ohlcv_df_1min = loader.load_data("XBT/USD", "1min")
    ohlcv_df_4hour = loader.load_data("XBT/USD", "4hour") # This will be empty unless dummy data is created for it

    # --- Backtest Trend-Following Mode ---
    if not ohlcv_df_4hour.empty:
        trend_strategy = TrendFollowingStrategy("XBT/USD", "240", 0.02) # 4-hour timeframe
        backtester_trend = BacktestingEngine(initial_capital=100000.0)
        await backtester_trend.run_backtest(trend_strategy, ohlcv_df_4hour)
    else:
        logging.warning("Skipping Trend-Following backtest: No 4-hour OHLCV data available.")

    # --- Backtest Scalping Mode ---
    if not ohlcv_df_1min.empty:
        scalping_strategy = ScalpingStrategy("XBT/USD", "1", 0.005) # 1-minute timeframe
        backtester_scalping = BacktestingEngine(initial_capital=100000.0)
        await backtester_scalping.run_backtest(scalping_strategy, ohlcv_df_1min)
    else:
        logging.warning("Skipping Scalping backtest: No 1-minute OHLCV data available.")

if __name__ == "__main__":
    asyncio.run(main())


