# Kraken Multi-Agent Trading System Makefile

.PHONY: help install test run clean setup dev lint format check

# Default target
help:
	@echo "Kraken Multi-Agent Trading System"
	@echo "=================================="
	@echo ""
	@echo "Available commands:"
	@echo "  make install    - Install dependencies and set up the system"
	@echo "  make test       - Run all tests"
	@echo "  make run        - Start the trading system"
	@echo "  make sandbox    - Run in sandbox mode (paper trading)"
	@echo "  make config     - Show current configuration"
	@echo "  make clean      - Clean up generated files"
	@echo "  make setup      - Set up development environment"
	@echo "  make dev        - Install development dependencies"
	@echo "  make lint       - Run code linting"
	@echo "  make format     - Format code with black"
	@echo "  make check      - Run all quality checks"
	@echo ""
	@echo "Quick start:"
	@echo "  1. make install"
	@echo "  2. Edit .env file with your API keys"
	@echo "  3. make sandbox"

# Installation and setup
install:
	@echo "Installing Kraken Multi-Agent Trading System..."
	python install.py

setup: install
	@echo "Setting up development environment..."
	pip install -e .[dev]

dev:
	@echo "Installing development dependencies..."
	pip install pytest pytest-asyncio black flake8 mypy pre-commit
	pre-commit install

# Testing
test:
	@echo "Running system tests..."
	python test_system.py

test-unit:
	@echo "Running unit tests..."
	python -m pytest tests/ -v

test-integration:
	@echo "Running integration tests..."
	python -m pytest tests/integration/ -v

# Running the system
run:
	@echo "Starting trading system..."
	python run.py

sandbox:
	@echo "Starting in sandbox mode (paper trading)..."
	python run.py --sandbox

live:
	@echo "Starting in LIVE mode (REAL MONEY)..."
	python run.py --live

config:
	@echo "Showing current configuration..."
	python run.py --config

# Code quality
lint:
	@echo "Running code linting..."
	flake8 src/ tests/ --max-line-length=100 --ignore=E203,W503

format:
	@echo "Formatting code..."
	black src/ tests/ *.py --line-length=100

type-check:
	@echo "Running type checking..."
	mypy src/ --ignore-missing-imports

check: lint type-check test
	@echo "All quality checks completed!"

# Maintenance
clean:
	@echo "Cleaning up generated files..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/ dist/ .pytest_cache/ .mypy_cache/
	rm -rf logs/*.log

clean-logs:
	@echo "Cleaning log files..."
	rm -rf logs/*.log

clean-data:
	@echo "Cleaning cached data..."
	rm -rf data/*

# Documentation
docs:
	@echo "Generating documentation..."
	@echo "Documentation is in README.md"

# Backup and restore
backup:
	@echo "Creating backup..."
	tar -czf backup_$(shell date +%Y%m%d_%H%M%S).tar.gz \
		--exclude='logs' --exclude='data' --exclude='__pycache__' \
		--exclude='.git' --exclude='*.pyc' .

# Docker operations (if using Docker)
docker-build:
	@echo "Building Docker image..."
	docker build -t kraken-trading .

docker-run:
	@echo "Running in Docker container..."
	docker run -it --rm -v $(PWD)/.env:/app/.env kraken-trading

# Environment management
env-template:
	@echo "Creating .env template..."
	python -c "from src.utils.config import create_env_template; create_env_template()"

env-check:
	@echo "Checking environment configuration..."
	python -c "from src.utils.config import load_config; config = load_config(); print('✓ Configuration valid')"

# Performance monitoring
monitor:
	@echo "Starting system monitor..."
	@echo "Use Ctrl+C to stop monitoring"
	watch -n 5 'ps aux | grep python | grep -v grep; echo ""; df -h; echo ""; free -h'

# Quick development workflow
dev-setup: dev env-template
	@echo "Development environment ready!"
	@echo "Next steps:"
	@echo "1. Copy .env.template to .env"
	@echo "2. Edit .env with your settings"
	@echo "3. Run 'make test' to verify setup"
	@echo "4. Run 'make sandbox' to start trading"

# Release preparation
release-check: clean check test
	@echo "Release checks completed successfully!"

# Show system status
status:
	@echo "System Status:"
	@echo "=============="
	@python -c "import os; print(f'Python: {os.sys.version}')"
	@python -c "import os; print(f'Working Directory: {os.getcwd()}')"
	@echo -n "Environment file: "
	@if [ -f .env ]; then echo "✓ Found"; else echo "❌ Missing"; fi
	@echo -n "Source directory: "
	@if [ -d src ]; then echo "✓ Found"; else echo "❌ Missing"; fi
	@echo -n "Dependencies: "
	@python -c "import sys; import pkg_resources; print('✓ Installed')" 2>/dev/null || echo "❌ Missing"

# Emergency stop (if system is running in background)
stop:
	@echo "Stopping any running trading processes..."
	pkill -f "python.*main.py" || echo "No trading processes found"

# Show logs
logs:
	@echo "Recent system logs:"
	@echo "=================="
	@if [ -d logs ]; then tail -n 50 logs/*.log 2>/dev/null || echo "No log files found"; else echo "Logs directory not found"; fi

# System information
info:
	@echo "Kraken Multi-Agent Trading System Information"
	@echo "============================================="
	@echo "Version: 1.0.0"
	@echo "Python: $(shell python --version)"
	@echo "Platform: $(shell python -c 'import platform; print(platform.system())')"
	@echo "Architecture: $(shell python -c 'import platform; print(platform.machine())')"
	@echo ""
	@make status
