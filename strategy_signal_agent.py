import asyncio
import json
import websockets
import logging
from collections import defaultdict
import pandas as pd
from abc import ABC, abstractmethod

logging.basicConfig(level=logging.INFO, format=\'%(asctime)s - %(levelname)s - %(message)s\')

# --- MarketDataAgent (for context, assuming it's in a separate file or accessible) ---
# This part is included for completeness but ideally MarketDataAgent would be imported.
class MarketDataAgent:
    def __init__(self, websocket_url="wss://ws.kraken.com/"):
        self.websocket_url = websocket_url
        self.connection = None
        self.subscriptions = {}
        self.order_books = defaultdict(lambda: {\'bids\': {}, \'asks\': {}})
        self.ohlcv_data = defaultdict(list)
        self.data_queues = {
            "book": asyncio.Queue(),
            "ohlc": asyncio.Queue()
        }
        logging.info(f"MarketDataAgent initialized with WebSocket URL: {self.websocket_url}")

    async def connect(self):
        logging.info("Attempting to connect to Kraken WebSocket...")
        try:
            self.connection = await websockets.connect(self.websocket_url)
            logging.info("Successfully connected to Kraken WebSocket.")
            return True
        except Exception as e:
            logging.error(f"Failed to connect to WebSocket: {e}")
            return False

    async def disconnect(self):
        if self.connection:
            await self.connection.close()
            logging.info("Disconnected from Kraken WebSocket.")

    async def subscribe(self, channel, pair, interval=None):
        subscription_message = {
            "event": "subscribe",
            "pair": [pair],
            "subscription": {
                "name": channel
            }
        }
        if interval:
            subscription_message["subscription"]["interval"] = interval

        if self.connection:
            await self.connection.send(json.dumps(subscription_message))
            self.subscriptions[f"{channel}-{pair}-{interval}"] = subscription_message
            logging.info(f"Subscribed to {channel} for {pair} (interval: {interval}).")
        else:
            logging.warning("Not connected to WebSocket. Cannot subscribe.")

    async def parse_and_process_data(self, data):
        if isinstance(data, list) and len(data) > 1:
            channel_id = data[0]
            message_type = data[1]
            pair = data[-1]

            if message_type == \'book\':
                # logging.info(f"Order Book update for {pair}: {data}")
                await self.data_queues["book"].put({"type": "book", "pair": pair, "data": data})

            elif message_type == \'ohlc\':
                ohlcv = data[1]
                # logging.info(f"OHLCV data for {pair}: {ohlcv}")
                self.ohlcv_data[pair].append(ohlcv)
                await self.data_queues["ohlc"].put({"type": "ohlc", "pair": pair, "data": ohlcv})

            elif message_type == \'heartbeat\':
                logging.debug("Received heartbeat.")
            else:
                logging.info(f"Received unhandled message type: {message_type} for {pair}: {data}")
        elif isinstance(data, dict) and data.get("event") == "pong":
            logging.debug("Received pong.")
        elif isinstance(data, dict) and data.get("event") == "subscriptionStatus":
            logging.info(f"Subscription status: {data}")
        else:
            logging.info(f"Received unhandled message format: {data}")

    async def listen_for_data(self):
        if not self.connection:
            logging.error("Not connected to WebSocket. Cannot listen for data.")
            return

        try:
            async for message in self.connection:
                data = json.loads(message)
                await self.parse_and_process_data(data)
        except websockets.exceptions.ConnectionClosedOK:
            logging.info("WebSocket connection closed gracefully.")
        except Exception as e:
            logging.error(f"Error while listening for data: {e}")

    async def run(self):
        while True:
            if not self.connection or not self.connection.open:
                if not await self.connect():
                    await asyncio.sleep(5)
                    continue
                for sub_key, sub_msg in self.subscriptions.items():
                    logging.info(f"Re-subscribing to {sub_key}...")
                    await self.connection.send(json.dumps(sub_msg))

            await self.listen_for_data()
            await asyncio.sleep(1)

    async def get_data_queue(self, data_type):
        if data_type in self.data_queues:
            return self.data_queues[data_type]
        else:
            raise ValueError(f"Unknown data type: {data_type}")

# --- BaseStrategy --- 
class BaseStrategy(ABC):
    def __init__(self, pair: str, timeframe: str, risk_percentage: float):
        self.pair = pair
        self.timeframe = timeframe
        self.risk_percentage = risk_percentage
        self.signals = []

    @abstractmethod
    async def on_ohlcv_update(self, ohlcv_data: dict):
        pass

    @abstractmethod
    async def on_order_book_update(self, order_book_data: dict):
        pass

    @abstractmethod
    def get_signals(self) -> list:
        pass

    @abstractmethod
    def get_required_data_streams(self) -> dict:
        pass

    def clear_signals(self):
        self.signals = []

# --- TrendFollowingStrategy ---
class TrendFollowingStrategy(BaseStrategy):
    def __init__(self, pair: str, timeframe: str, risk_percentage: float, 
                 ema_short_period: int = 50, ema_long_period: int = 200, 
                 rsi_period: int = 14, rsi_overbought: int = 70, rsi_oversold: int = 30):
        super().__init__(pair, timeframe, risk_percentage)
        self.ema_short_period = ema_short_period
        self.ema_long_period = ema_long_period
        self.rsi_period = rsi_period
        self.rsi_overbought = rsi_overbought
        self.rsi_oversold = rsi_oversold
        self.prices = pd.Series(dtype=float)

    async def on_ohlcv_update(self, ohlcv_data: dict):
        try:
            close_price = float(ohlcv_data[4])
            timestamp = float(ohlcv_data[0])
            self.prices = pd.concat([self.prices, pd.Series([close_price], index=[timestamp])])
            self.prices = self.prices.iloc[-self.ema_long_period*2:]

            if len(self.prices) < max(self.ema_long_period, self.rsi_period):
                logging.info(f"Not enough data for {self.pair} to calculate indicators. Current data points: {len(self.prices)}")
                return

            ema_short = self.prices.ewm(span=self.ema_short_period, adjust=False).mean().iloc[-1]
            ema_long = self.prices.ewm(span=self.ema_long_period, adjust=False).mean().iloc[-1]

            delta = self.prices.diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.ewm(span=self.rsi_period, adjust=False).mean()
            avg_loss = loss.ewm(span=self.rsi_period, adjust=False).mean()
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs)).iloc[-1]

            # logging.info(f"[{self.pair}-{self.timeframe}] EMA_Short: {ema_short:.2f}, EMA_Long: {ema_long:.2f}, RSI: {rsi:.2f}")

            if ema_short > ema_long and rsi < self.rsi_oversold:
                signal = {
                    \'type\': \'entry\',
                    \'pair\': self.pair,
                    \'price\': close_price,
                    \'timestamp\': timestamp,
                    \'strategy_id\': \'trend_following\',
                    \'risk_percentage\': self.risk_percentage
                }
                self.signals.append(signal)
                logging.info(f"Generated BUY signal for {self.pair}: {signal}")
            elif ema_short < ema_long and rsi > self.rsi_overbought:
                signal = {
                    \'type\': \'entry\',
                    \'pair\': self.pair,
                    \'price\': close_price,
                    \'timestamp\': timestamp,
                    \'strategy_id\': \'trend_following\',
                    \'risk_percentage\': self.risk_percentage
                }
                # self.signals.append(signal)
                logging.info(f"Generated SELL signal for {self.pair}: {signal}")

        except Exception as e:
            logging.error(f"Error processing OHLCV data in TrendFollowingStrategy: {e}")

    async def on_order_book_update(self, order_book_data: dict):
        pass

    def get_signals(self) -> list:
        return self.signals

    def get_required_data_streams(self) -> dict:
        return {
            \'ohlc\': [(self.pair, self.timeframe)]
        }

# --- ScalpingStrategy ---
class ScalpingStrategy(BaseStrategy):
    def __init__(self, pair: str, timeframe: str, risk_percentage: float,
                 bollinger_period: int = 20, bollinger_std_dev: float = 2.0,
                 volume_threshold_multiplier: float = 1.5):
        super().__init__(pair, timeframe, risk_percentage)
        self.bollinger_period = bollinger_period
        self.bollinger_std_dev = bollinger_std_dev
        self.volume_threshold_multiplier = volume_threshold_multiplier
        self.prices = pd.Series(dtype=float)
        self.volumes = pd.Series(dtype=float)

    async def on_ohlcv_update(self, ohlcv_data: dict):
        try:
            close_price = float(ohlcv_data[4])
            volume = float(ohlcv_data[6])
            timestamp = float(ohlcv_data[0])

            self.prices = pd.concat([self.prices, pd.Series([close_price], index=[timestamp])])
            self.volumes = pd.concat([self.volumes, pd.Series([volume], index=[timestamp])])

            self.prices = self.prices.iloc[-self.bollinger_period*2:]
            self.volumes = self.volumes.iloc[-self.bollinger_period*2:]

            if len(self.prices) < self.bollinger_period:
                logging.info(f"Not enough data for {self.pair} to calculate Bollinger Bands. Current data points: {len(self.prices)}")
                return

            rolling_mean = self.prices.rolling(window=self.bollinger_period).mean()
            rolling_std = self.prices.rolling(window=self.bollinger_period).std()
            upper_band = rolling_mean + (rolling_std * self.bollinger_std_dev)
            lower_band = rolling_mean - (rolling_std * self.bollinger_std_dev)

            current_mean = rolling_mean.iloc[-1]
            current_upper = upper_band.iloc[-1]
            current_lower = lower_band.iloc[-1]

            avg_volume = self.volumes.mean()
            is_high_volume = volume > (avg_volume * self.volume_threshold_multiplier)

            # logging.info(f"[{self.pair}-{self.timeframe}] Close: {close_price:.2f}, Mean: {current_mean:.2f}, Upper: {current_upper:.2f}, Lower: {current_lower:.2f}, Volume: {volume:.2f}, High Volume: {is_high_volume}")

            if close_price < current_lower and is_high_volume:
                signal = {
                    \'type\': \'entry\',
                    \'pair\': self.pair,
                    \'price\': close_price,
                    \'timestamp\': timestamp,
                    \'strategy_id\': \'scalping\',
                    \'risk_percentage\': self.risk_percentage
                }
                self.signals.append(signal)
                logging.info(f"Generated BUY signal (Scalping) for {self.pair}: {signal}")
            elif close_price > current_upper and is_high_volume:
                signal = {
                    \'type\': \'entry\',
                    \'pair\': self.pair,
                    \'price\': close_price,
                    \'timestamp\': timestamp,
                    \'strategy_id\': \'scalping\',
                    \'risk_percentage\': self.risk_percentage
                }
                # self.signals.append(signal)
                logging.info(f"Generated SELL signal (Scalping) for {self.pair}: {signal}")

        except Exception as e:
            logging.error(f"Error processing OHLCV data in ScalpingStrategy: {e}")

    async def on_order_book_update(self, order_book_data: dict):
        pass

    def get_signals(self) -> list:
        return self.signals

    def get_required_data_streams(self) -> dict:
        return {
            \'ohlc\': [(self.pair, self.timeframe)]
        }

# --- StrategySignalAgent ---
class StrategySignalAgent:
    def __init__(self, market_data_agent: MarketDataAgent, operating_mode: str, trading_pairs: list):
        self.market_data_agent = market_data_agent
        self.operating_mode = operating_mode
        self.trading_pairs = trading_pairs
        self.strategies = {}
        self.signal_queue = asyncio.Queue() # To send signals to Risk & Execution Agent
        self._initialize_strategies()

    def _initialize_strategies(self):
        for pair in self.trading_pairs:
            if self.operating_mode == "trend_following":
                # Default parameters for Trend-Following
                self.strategies[pair] = TrendFollowingStrategy(pair, "240", 0.02) # 4-hour timeframe
                logging.info(f"Initialized Trend-Following Strategy for {pair}")
            elif self.operating_mode == "scalping":
                # Default parameters for Scalping
                self.strategies[pair] = ScalpingStrategy(pair, "1", 0.005) # 1-minute timeframe
                logging.info(f"Initialized Scalping Strategy for {pair}")
            else:
                raise ValueError(f"Unknown operating mode: {self.operating_mode}")

    async def run(self):
        # Subscribe to required data streams from MarketDataAgent
        for pair, strategy in self.strategies.items():
            required_streams = strategy.get_required_data_streams()
            for channel, pairs_intervals in required_streams.items():
                for p, interval in pairs_intervals:
                    await self.market_data_agent.subscribe(channel, p, interval)

        # Start consuming data from MarketDataAgent queues
        ohlc_queue = await self.market_data_agent.get_data_queue("ohlc")
        book_queue = await self.market_data_agent.get_data_queue("book")

        asyncio.create_task(self._consume_ohlc_data(ohlc_queue))
        asyncio.create_task(self._consume_book_data(book_queue))

        logging.info("StrategySignalAgent started listening for market data.")

    async def _consume_ohlc_data(self, ohlc_queue):
        while True:
            data = await ohlc_queue.get()
            pair = data["pair"]
            ohlcv_data = data["data"]
            if pair in self.strategies:
                await self.strategies[pair].on_ohlcv_update(ohlcv_data)
                await self._check_and_forward_signals(self.strategies[pair])

    async def _consume_book_data(self, book_queue):
        while True:
            data = await book_queue.get()
            pair = data["pair"]
            book_data = data["data"]
            if pair in self.strategies:
                await self.strategies[pair].on_order_book_update(book_data)
                await self._check_and_forward_signals(self.strategies[pair])

    async def _check_and_forward_signals(self, strategy: BaseStrategy):
        signals = strategy.get_signals()
        if signals:
            for signal in signals:
                await self.signal_queue.put(signal)
                logging.info(f"Forwarded signal to Risk & Execution Agent: {signal}")
            strategy.clear_signals()

    async def get_signal_queue(self):
        return self.signal_queue

async def main():
    # Example usage
    mda = MarketDataAgent()
    # Start MDA in a separate task
    asyncio.create_task(mda.run())

    # Initialize SSA for Trend-Following mode with XBT/USD
    ssa_trend = StrategySignalAgent(mda, "trend_following", ["XBT/USD"])
    asyncio.create_task(ssa_trend.run())

    # Initialize SSA for Scalping mode with ETH/USD
    ssa_scalp = StrategySignalAgent(mda, "scalping", ["ETH/USD"])
    asyncio.create_task(ssa_scalp.run())

    # Simulate a Risk & Execution Agent consuming signals
    async def signal_consumer(signal_queue, agent_name):
        while True:
            signal = await signal_queue.get()
            logging.warning(f"[{agent_name}] Received signal: {signal}")

    signal_queue_trend = await ssa_trend.get_signal_queue()
    signal_queue_scalp = await ssa_scalp.get_signal_queue()

    asyncio.create_task(signal_consumer(signal_queue_trend, "RiskExecAgent-Trend"))
    asyncio.create_task(signal_consumer(signal_queue_scalp, "RiskExecAgent-Scalp"))

    # Keep the main loop running indefinitely
    while True:
        await asyncio.sleep(3600) # Sleep for an hour

if __name__ == "__main__":
    asyncio.run(main())


