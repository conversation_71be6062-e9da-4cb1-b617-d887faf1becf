#!/usr/bin/env python3
"""
Trading Configurator for Kraken Multi-Agent Live Trading System
Interactive configuration tool for setting up trading parameters
"""

import os
import sys
from pathlib import Path

def print_header():
    """Print configurator header"""
    print("=" * 80)
    print("         KRAKEN MULTI-AGENT LIVE TRADING SYSTEM CONFIGURATOR")
    print("                    ⚠️  REAL MONEY AT RISK ⚠️")
    print("=" * 80)
    print()

def print_section(title):
    """Print section header"""
    print(f"\n{'-' * 60}")
    print(f" {title}")
    print(f"{'-' * 60}")

def get_input(prompt, default=None, input_type=str, validation=None):
    """Get user input with validation"""
    while True:
        if default is not None:
            user_input = input(f"{prompt} [{default}]: ").strip()
            if not user_input:
                user_input = str(default)
        else:
            user_input = input(f"{prompt}: ").strip()
        
        if not user_input and default is None:
            print("❌ This field is required!")
            continue
        
        try:
            # Convert to appropriate type
            if input_type == float:
                value = float(user_input)
            elif input_type == int:
                value = int(user_input)
            else:
                value = user_input
            
            # Run validation if provided
            if validation and not validation(value):
                print("❌ Invalid input! Please try again.")
                continue
            
            return value
        except ValueError:
            print(f"❌ Please enter a valid {input_type.__name__}!")

def configure_api_keys():
    """Configure Kraken API keys"""
    print_section("KRAKEN API CONFIGURATION")
    print("Enter your 5 Kraken API keys and secrets.")
    print("Get them from: https://www.kraken.com/u/security/api")
    print("⚠️  Make sure they have trading permissions enabled!")
    print()
    
    api_keys = []
    api_secrets = []
    
    for i in range(1, 6):
        print(f"\nAPI Key Pair #{i}:")
        
        while True:
            api_key = get_input(f"  API Key {i}")
            if len(api_key) < 10:
                print("  ❌ API key seems too short. Please check and try again.")
                continue
            break
        
        while True:
            api_secret = get_input(f"  API Secret {i}")
            if len(api_secret) < 20:
                print("  ❌ API secret seems too short. Please check and try again.")
                continue
            break
        
        api_keys.append(api_key)
        api_secrets.append(api_secret)
        print(f"  ✓ API Key Pair #{i} configured")
    
    return api_keys, api_secrets

def configure_trading_parameters():
    """Configure trading parameters"""
    print_section("TRADING CONFIGURATION")
    
    # Operating mode
    print("Select operating mode:")
    print("1. Trend Following (4-hour timeframe, EMA + RSI)")
    print("2. Scalping (1-minute timeframe, Bollinger Bands + Volume)")
    
    while True:
        mode_choice = get_input("Choose mode (1 or 2)", "1")
        if mode_choice in ["1", "2"]:
            operating_mode = "trend_following" if mode_choice == "1" else "scalping"
            break
        print("❌ Please enter 1 or 2")
    
    # Trading pairs
    print("\nTrading pairs (comma-separated):")
    print("Available: XBT/USD, ETH/USD, ADA/USD, DOT/USD, LINK/USD, etc.")
    trading_pairs = get_input("Trading pairs", "XBT/USD,ETH/USD")
    
    # Initial portfolio value
    initial_portfolio = get_input(
        "Initial portfolio value (USD)", 
        10000.0, 
        float,
        lambda x: x > 0
    )
    
    return operating_mode, trading_pairs, initial_portfolio

def configure_risk_management():
    """Configure risk management parameters"""
    print_section("RISK MANAGEMENT")
    
    # Maximum drawdown
    max_drawdown = get_input(
        "Maximum portfolio drawdown (0.0-1.0, e.g., 0.5 = 50%)", 
        0.50, 
        float,
        lambda x: 0.0 < x <= 1.0
    )
    
    # Maximum position size
    max_position = get_input(
        "Maximum position size (0.0-1.0, e.g., 0.1 = 10% of portfolio)", 
        0.10, 
        float,
        lambda x: 0.0 < x <= 1.0
    )
    
    # Stop loss
    stop_loss = get_input(
        "Stop loss percentage (0.0-1.0, e.g., 0.02 = 2%)", 
        0.02, 
        float,
        lambda x: 0.0 < x <= 1.0
    )
    
    # Take profit
    take_profit = get_input(
        "Take profit percentage (0.0-1.0, e.g., 0.04 = 4%)", 
        0.04, 
        float,
        lambda x: 0.0 < x <= 1.0
    )
    
    return max_drawdown, max_position, stop_loss, take_profit

def configure_system_settings():
    """Configure system settings"""
    print_section("SYSTEM SETTINGS")
    
    # Log level
    print("Log level:")
    print("1. INFO (recommended)")
    print("2. DEBUG (verbose)")
    print("3. WARNING (minimal)")
    
    while True:
        log_choice = get_input("Choose log level (1, 2, or 3)", "1")
        if log_choice in ["1", "2", "3"]:
            log_levels = {"1": "INFO", "2": "DEBUG", "3": "WARNING"}
            log_level = log_levels[log_choice]
            break
        print("❌ Please enter 1, 2, or 3")
    
    # API rate limit
    api_rate_limit = get_input(
        "API rate limit (requests per minute)", 
        20, 
        int,
        lambda x: 1 <= x <= 100
    )
    
    # Dashboard refresh rate
    dashboard_refresh = get_input(
        "Dashboard refresh rate (seconds)", 
        1.0, 
        float,
        lambda x: 0.1 <= x <= 10.0
    )
    
    return log_level, api_rate_limit, dashboard_refresh

def create_env_file(config_data):
    """Create .env file with configuration"""
    print_section("CREATING CONFIGURATION FILE")
    
    api_keys, api_secrets = config_data['api_keys'], config_data['api_secrets']
    
    env_content = f"""# Kraken Multi-Agent Live Trading System Configuration
# ⚠️  LIVE TRADING ONLY - REAL MONEY AT RISK ⚠️
# Generated by Trading Configurator

# =============================================================================
# KRAKEN API CONFIGURATION - LIVE TRADING
# =============================================================================

KRAKEN_API_KEY_1={api_keys[0]}
KRAKEN_API_SECRET_1={api_secrets[0]}

KRAKEN_API_KEY_2={api_keys[1]}
KRAKEN_API_SECRET_2={api_secrets[1]}

KRAKEN_API_KEY_3={api_keys[2]}
KRAKEN_API_SECRET_3={api_secrets[2]}

KRAKEN_API_KEY_4={api_keys[3]}
KRAKEN_API_SECRET_4={api_secrets[3]}

KRAKEN_API_KEY_5={api_keys[4]}
KRAKEN_API_SECRET_5={api_secrets[4]}

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================

MAX_PORTFOLIO_DRAWDOWN={config_data['max_drawdown']}
OPERATING_MODE={config_data['operating_mode']}
TRADING_PAIRS={config_data['trading_pairs']}
INITIAL_PORTFOLIO_VALUE={config_data['initial_portfolio']}

# =============================================================================
# RISK MANAGEMENT
# =============================================================================

MAX_POSITION_SIZE={config_data['max_position']}
STOP_LOSS_PERCENTAGE={config_data['stop_loss']}
TAKE_PROFIT_PERCENTAGE={config_data['take_profit']}

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================

LOG_LEVEL={config_data['log_level']}
WEBSOCKET_URL=wss://ws.kraken.com/
API_RATE_LIMIT={config_data['api_rate_limit']}
DASHBOARD_REFRESH_RATE={config_data['dashboard_refresh']}
"""
    
    # Backup existing .env if it exists
    if Path(".env").exists():
        backup_name = ".env.backup"
        counter = 1
        while Path(backup_name).exists():
            backup_name = f".env.backup.{counter}"
            counter += 1
        
        os.rename(".env", backup_name)
        print(f"✓ Existing .env backed up as {backup_name}")
    
    # Write new .env file
    with open(".env", "w") as f:
        f.write(env_content)
    
    print("✓ Configuration saved to .env file")

def display_summary(config_data):
    """Display configuration summary"""
    print_section("CONFIGURATION SUMMARY")
    
    print(f"Operating Mode:       {config_data['operating_mode']}")
    print(f"Trading Pairs:        {config_data['trading_pairs']}")
    print(f"Initial Portfolio:    ${config_data['initial_portfolio']:,.2f}")
    print(f"Max Drawdown:         {config_data['max_drawdown']:.1%}")
    print(f"Max Position Size:    {config_data['max_position']:.1%}")
    print(f"Stop Loss:            {config_data['stop_loss']:.1%}")
    print(f"Take Profit:          {config_data['take_profit']:.1%}")
    print(f"Log Level:            {config_data['log_level']}")
    print(f"API Rate Limit:       {config_data['api_rate_limit']} req/min")
    print(f"Dashboard Refresh:    {config_data['dashboard_refresh']}s")
    print(f"API Keys Configured:  5")

def main():
    """Main configurator function"""
    print_header()
    
    print("This configurator will help you set up the live trading system.")
    print("⚠️  WARNING: This system trades with REAL MONEY!")
    print()
    
    confirm = input("Do you want to continue? (yes/no): ").lower().strip()
    if confirm not in ['yes', 'y']:
        print("Configuration cancelled.")
        return
    
    try:
        # Collect configuration
        config_data = {}
        
        # API Keys
        api_keys, api_secrets = configure_api_keys()
        config_data['api_keys'] = api_keys
        config_data['api_secrets'] = api_secrets
        
        # Trading parameters
        operating_mode, trading_pairs, initial_portfolio = configure_trading_parameters()
        config_data['operating_mode'] = operating_mode
        config_data['trading_pairs'] = trading_pairs
        config_data['initial_portfolio'] = initial_portfolio
        
        # Risk management
        max_drawdown, max_position, stop_loss, take_profit = configure_risk_management()
        config_data['max_drawdown'] = max_drawdown
        config_data['max_position'] = max_position
        config_data['stop_loss'] = stop_loss
        config_data['take_profit'] = take_profit
        
        # System settings
        log_level, api_rate_limit, dashboard_refresh = configure_system_settings()
        config_data['log_level'] = log_level
        config_data['api_rate_limit'] = api_rate_limit
        config_data['dashboard_refresh'] = dashboard_refresh
        
        # Display summary
        display_summary(config_data)
        
        # Confirm and save
        print()
        confirm_save = input("Save this configuration? (yes/no): ").lower().strip()
        if confirm_save in ['yes', 'y']:
            create_env_file(config_data)
            
            print()
            print("=" * 80)
            print("                    CONFIGURATION COMPLETE!")
            print("=" * 80)
            print()
            print("Next steps:")
            print("1. Review your configuration in the .env file")
            print("2. Run the trading system: python main.py")
            print("3. Or use the batch file: launch_trading_system.bat")
            print()
            print("⚠️  Remember: This system trades with REAL MONEY!")
            print("=" * 80)
        else:
            print("Configuration not saved.")
    
    except KeyboardInterrupt:
        print("\n\nConfiguration cancelled by user.")
    except Exception as e:
        print(f"\n❌ Error during configuration: {e}")

if __name__ == "__main__":
    main()
