# Risk & Execution Agent: Design for Order Placement and Management Module

## 1. Overview

The Risk & Execution Agent (REA) serves as the central control unit of the Kraken Multi-Agent Cryptocurrency Trading System. Its primary responsibilities include receiving trading signals from the Strategy & Signal Agent, performing critical pre-trade risk checks, managing API interactions with the Kraken exchange, and ensuring the safe and efficient execution of trades. This document focuses on the design of the order placement and management module, which is at the core of the REA's operational capabilities.

## 2. Core Responsibilities of the Order Placement and Management Module

The order placement and management module within the REA is responsible for:

-   **Signal Ingestion**: Receiving and processing trading signals (entry, take-profit, stop-loss) from the Strategy & Signal Agent.
-   **Pre-Trade Risk Verification**: Implementing and enforcing critical risk management rules before any order is placed. This includes position sizing and overall portfolio drawdown limits.
-   **Order Construction**: Translating validated signals into executable Kraken API order parameters.
-   **Order Placement**: Interacting with the Kraken API to submit new orders.
-   **Order Tracking and State Management**: Maintaining an accurate, real-time record of all open orders and their current status (e.g., pending, open, filled, canceled).
-   **Order Modification/Cancellation**: Handling requests to modify or cancel existing orders based on risk management protocols or strategy adjustments.
-   **Position Management**: Tracking current open positions, including entry price, quantity, and associated profit/loss.
-   **Communication with Dashboard Agent**: Providing real-time updates on order status, trade executions, and position changes to the Dashboard Agent.

## 3. Order Flow and Processing

The following steps outline the typical flow of an order through the REA's order placement and management module:

1.  **Signal Reception**: The REA continuously monitors a dedicated input queue for new trading signals from the Strategy & Signal Agent. Each signal contains details such as `type` (entry, take_profit, stop_loss), `pair`, `price`, `timestamp`, `strategy_id`, and `risk_percentage`.

2.  **Pre-Trade Risk Checks (Sequential and Critical)**:
    *   **Position Sizing Rule Verification**: Before an entry order is placed, the module calculates the appropriate trade size based on the `risk_percentage` provided in the signal and the current total portfolio equity. It ensures that the calculated trade size adheres to predefined position sizing rules (e.g., maximum capital allocated per trade, minimum trade size).
    *   **Master Max Portfolio Drawdown Limit Verification**: The module checks if placing the proposed trade would cause the potential maximum drawdown of the entire portfolio to exceed the `max_portfolio_drawdown` limit defined by the user. If the potential loss from this trade, combined with existing open positions, would breach this limit, the trade is rejected.

3.  **Order Construction**: If all risk checks pass, the module constructs the Kraken API order request. This involves:
    *   Determining the order type (e.g., `limit`, `market`). For entry signals, it will likely be a limit order at the signaled price or a market order if immediate execution is prioritized.
    *   Calculating the exact quantity to trade based on the position sizing. This will involve converting the calculated risk amount into a quantity of the base currency.
    *   Setting up associated take-profit and stop-loss orders (often as OCO - One-Cancels-the-Other - orders if supported by Kraken, or managed client-side).

4.  **Order Placement**: The constructed order is then sent to the Kraken API via the API management framework (which handles key rotation and rate limiting). The module will wait for the API response to confirm order submission.

5.  **Order Tracking and State Management**: Upon successful submission, the order's details (Kraken order ID, type, status, quantity, price) are stored in an in-memory order book. This internal state is continuously updated based on WebSocket private feed updates (e.g., `ownTrades`, `openOrders`) or periodic REST API queries.

6.  **Position Management**: When an entry order is fully filled, a new position is opened. The module tracks all open positions, including the average entry price, current quantity, and real-time unrealized profit/loss. When a take-profit or stop-loss order is filled, the corresponding position is closed.

7.  **Order Modification/Cancellation**: The module will expose methods to modify or cancel orders. This is crucial for implementing risk management actions like the 


Master Circuit Breaker or Volatility Halt. These actions will trigger API calls to cancel open orders or close positions.

## 4. Risk Management Integration

### Position Sizing Rule

-   **Rule**: No single trade shall risk more than a user-defined percentage of total portfolio equity (e.g., 2% for Trend-Following, 0.5% for Scalping). This percentage is provided by the Strategy & Signal Agent with each signal.
-   **Calculation**: The REA will calculate the maximum allowable loss for a trade based on the `risk_percentage` and the current `total_equity`. This loss amount is then used to determine the position size (quantity) given the entry price and the stop-loss level. The formula for calculating position size will be:

    ```
    Position Size (Units) = (Total Equity * Risk Percentage) / (Entry Price - Stop Loss Price)
    ```

    If the calculated `Position Size` is less than Kraken's minimum order size for the `pair`, the trade will be rejected. Similarly, if it exceeds a predefined maximum position size, it will be capped or rejected.

### Master Max Portfolio Drawdown Limit

-   **Rule**: The user must define a `max_portfolio_drawdown` percentage (e.g., 50%). If the total account equity drops to this level, the REA must trigger a "Total Halt": cancel all orders, close all positions, and cease all trading.
-   **Monitoring**: The REA will continuously monitor the `TOTAL EQUITY` (which includes realized and unrealized P/L). This requires fetching account balance information from Kraken (via REST API or private WebSocket if available) and combining it with the unrealized P/L of open positions.
-   **Trigger**: When `Current Equity <= Initial Equity * (1 - max_portfolio_drawdown)`, the "Total Halt" is activated.
-   **Action (Total Halt)**:
    1.  **Cancel All Open Orders**: Immediately send API calls to cancel all active orders on Kraken.
    2.  **Close All Positions**: Execute market orders to close all open long and short positions.
    3.  **Cease Trading**: Set an internal flag to `trading_halted = True`, preventing any new signals from being processed or orders from being placed.
    4.  **Notify Dashboard Agent**: Send a critical alert to the Dashboard Agent for immediate user notification.

### Volatility Halt

-   **Rule**: If the Market Data Agent detects a price change of more than 8% in any single monitored asset within a 5-minute period, the Risk & Execution Agent will enter a "Cool-Down" state for 15 minutes, rejecting new signals.
-   **Detection**: The MDA will be responsible for detecting significant price changes and signaling this event to the REA. The REA will then verify the condition.
-   **Action (Cool-Down)**:
    1.  **Reject New Signals**: Temporarily set an internal flag to `cool_down_active = True` for 15 minutes, causing the REA to ignore any incoming signals from the Strategy & Signal Agent.
    2.  **Maintain Existing Orders/Positions**: Unlike the "Total Halt," existing open orders and positions are not immediately affected, but no new trades will be initiated.
    3.  **Notify Dashboard Agent**: Inform the Dashboard Agent about the volatility halt and its duration.

## 5. Communication with Dashboard Agent

The REA will communicate critical information to the Dashboard Agent to ensure real-time visibility for the user. This will include:

-   **Order Status Updates**: Confirmation of order placement, fills, cancellations, and rejections.
-   **Position Updates**: Changes in open positions, including new entries, closures, and real-time unrealized P/L.
-   **Risk Management Alerts**: Notifications for triggered circuit breakers, volatility halts, or API errors.
-   **System Logs**: Detailed logs of all actions taken by the REA.

This communication will likely occur via a dedicated `asyncio.Queue` or similar IPC mechanism, ensuring non-blocking updates to the CLI dashboard.

## 6. API Interaction (Brief Overview - Detailed in next section)

The REA will be the sole agent responsible for direct interaction with the Kraken REST API for order placement, cancellation, and account information retrieval. This interaction will be mediated by a robust API management framework that handles:

-   **API Key Pool Management**: Utilizing multiple API keys for redundancy and load balancing.
-   **Intelligent Key Rotation**: Switching keys in a round-robin fashion and quarantining problematic keys.
-   **Rate-Limit Awareness**: Actively tracking API call costs and pausing requests to avoid exceeding Kraken's rate limits.

This separation of concerns ensures that the core logic of order placement and risk management is decoupled from the complexities of API interaction, which will be detailed in the next design document.

