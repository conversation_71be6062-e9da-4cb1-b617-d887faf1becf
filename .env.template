# Kraken Multi-Agent Cryptocurrency Trading System Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# KRAKEN API CONFIGURATION
# =============================================================================
# Get your API keys from: https://www.kraken.com/u/security/api

# Production API Keys (LIVE TRADING - REAL MONEY)
KRAKEN_API_KEY=your_production_api_key_here
KRAKEN_API_SECRET=your_production_api_secret_here

# Sandbox API Keys (PAPER TRADING - NO REAL MONEY)
KRAKEN_SANDBOX_API_KEY=your_sandbox_api_key_here
KRAKEN_SANDBOX_API_SECRET=your_sandbox_api_secret_here

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================

# Trading Mode: true = sandbox (paper trading), false = live trading
USE_SANDBOX=True

# Maximum portfolio drawdown before emergency halt (0.0 to 1.0)
# Example: 0.50 = 50% maximum drawdown
MAX_PORTFOLIO_DRAWDOWN=0.50

# Operating mode: "trend_following" or "scalping"
OPERATING_MODE=trend_following

# Trading pairs (comma-separated)
# Available pairs: XBT/USD, ETH/USD, ADA/USD, DOT/USD, etc.
TRADING_PAIRS=XBT/USD,ETH/USD

# =============================================================================
# RISK MANAGEMENT
# =============================================================================

# Maximum position size as percentage of portfolio (0.0 to 1.0)
MAX_POSITION_SIZE=0.1

# Stop loss percentage (0.0 to 1.0)
STOP_LOSS_PERCENTAGE=0.02

# Take profit percentage (0.0 to 1.0)
TAKE_PROFIT_PERCENTAGE=0.04

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================

# Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Kraken WebSocket URL (usually no need to change)
WEBSOCKET_URL=wss://ws.kraken.com/

# API rate limit (requests per minute)
API_RATE_LIMIT=20

# Dashboard refresh rate (seconds)
DASHBOARD_REFRESH_RATE=1.0

# =============================================================================
# ADVANCED SETTINGS (Optional)
# =============================================================================

# Strategy-specific parameters for trend following
TREND_EMA_SHORT_PERIOD=50
TREND_EMA_LONG_PERIOD=200
TREND_RSI_PERIOD=14
TREND_RSI_OVERBOUGHT=70
TREND_RSI_OVERSOLD=30

# Strategy-specific parameters for scalping
SCALPING_BOLLINGER_PERIOD=20
SCALPING_BOLLINGER_STD_DEV=2.0
SCALPING_VOLUME_THRESHOLD=1.5

# =============================================================================
# NOTES
# =============================================================================
# 
# IMPORTANT SECURITY NOTES:
# 1. Never commit the .env file to version control
# 2. Keep your API keys secure and never share them
# 3. Use sandbox mode for testing before going live
# 4. Start with small amounts when using live trading
# 
# GETTING STARTED:
# 1. Copy this file to .env
# 2. Fill in your Kraken API credentials
# 3. Set USE_SANDBOX=True for testing
# 4. Run: python main.py
# 
# For more information, see the README.md file
#
