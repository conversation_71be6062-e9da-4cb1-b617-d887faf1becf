# Risk & Execution Agent: Design for Resilient API Management Framework

## 1. Overview

The Risk & Execution Agent (REA) is the sole component of the trading system that directly interacts with the Kraken API for order placement, cancellation, and account information retrieval. To ensure maximum uptime, efficiency, and compliance with exchange rules, the REA incorporates a resilient API management framework. This framework is designed to handle API key pools, intelligently rotate keys, and manage request frequency to stay within Kraken's rate limits, thereby preventing temporary IP bans and ensuring continuous operation.

## 2. API Key Pool Management

The system will be initialized with a list of one or more user-provided Kraken API keys. This pool provides redundancy and allows for intelligent distribution of API calls.

-   **Configuration**: API keys (key-secret pairs) will be loaded securely, preferably from environment variables or a dedicated configuration file that is not committed to version control.
-   **Storage**: The REA will maintain an in-memory list of `KrakenAPIClient` instances, each initialized with a unique API key from the pool.
-   **Status Tracking**: Each key in the pool will have an associated status (e.g., `ACTIVE`, `QUARANTINED`, `DISABLED`).
    -   `ACTIVE`: The key is fully operational and available for use.
    -   `QUARANTINED`: The key has encountered a non-fatal error (e.g., nonce error, temporary rate limit hit) and is temporarily taken out of rotation for a defined period.
    -   `DISABLED`: The key has encountered a fatal error or repeated non-fatal errors, indicating a persistent issue, and is permanently removed from rotation until manual intervention.

## 3. Intelligent Key Rotation

For each new order or API action, the REA will select the next available API key from the pool in a round-robin fashion. This distributes the load across multiple keys and helps mitigate individual key-specific issues.

-   **Round-Robin Selection**: A simple counter will be used to cycle through the `ACTIVE` keys in the pool.
-   **Error Handling and Quarantine**: If an API call using a selected key returns a nonce error or another non-fatal API error (e.g., `EAPI:RateLimit` that is not account-wide), the following actions will be taken:
    1.  **Temporarily Quarantine Key**: The problematic key will be marked as `QUARANTINED` and a timestamp will be recorded, indicating when it can be re-activated.
    2.  **Immediate Retry**: The REA will immediately retry the failed API action with the *next available* `ACTIVE` key in the pool. This ensures that critical operations are not delayed by transient key issues.
    3.  **Re-activation Logic**: Quarantined keys will be automatically re-activated after a predefined cool-down period (e.g., 5 minutes). If a key repeatedly enters the `QUARANTINED` state within a short timeframe, it might be escalated to `DISABLED` status.
-   **Fatal Error Handling**: If an API call returns a fatal error (e.g., invalid API key, insufficient permissions), the key will be marked as `DISABLED` and an alert will be sent to the Dashboard Agent. The system will continue operating with the remaining `ACTIVE` keys.

## 4. Rate-Limit Awareness

Kraken imposes account-wide rate limits on API requests. Exceeding these limits can lead to temporary IP bans, severely impacting trading operations. The REA must actively track the API call cost associated with its actions and intelligently manage the request frequency to stay below these limits.

-   **Kraken Rate Limit Mechanism**: Kraken uses a 


cost-based rate limit system, where each API endpoint has an associated "cost." The total cost of requests within a rolling window (e.g., 20-30 seconds) must not exceed a certain threshold. The current rate limit status is often returned in API response headers.

-   **Cost Tracking**: The REA will maintain an internal counter for the total API call cost within the rolling window. Each time an API call is made, its known cost will be added to this counter.
-   **Dynamic Delay Mechanism**: Before making an API call, the REA will check the current cost counter against the allowed limit. If the projected cost of the next call would exceed the limit, the REA will introduce a dynamic delay (e.g., `asyncio.sleep()`) to wait until the rolling window clears enough cost to accommodate the new request. This proactive approach prevents hitting the rate limit.
-   **Response Header Monitoring**: The REA will parse API response headers (e.g., `X-Kraken-RateLimit-Remaining`, `X-Kraken-RateLimit-Reset`) to get real-time feedback on rate limit status. This information will be used to adjust the internal cost tracking and dynamic delay mechanism for optimal performance.
-   **Account-Wide vs. Key-Specific Limits**: It's crucial to distinguish between account-wide rate limits (which apply to all keys under the same account) and potential key-specific limits (less common but possible). The primary focus will be on managing the account-wide limit.

## 5. Error Handling and Reconciliation

-   **API/Exchange Error Handling**: If the Risk & Execution Agent exhausts all `ACTIVE` keys in its pool with consecutive failed API calls (e.g., all keys return `EAPI:RateLimit` or a similar critical error), it will assume a systemic issue with the exchange or connectivity.
    1.  **Halt Placing New Trades**: The REA will set `trading_halted = True` and enter a `reconciliation_state`.
    2.  **Enter Reconciliation State**: In this state, the REA will cease attempting new trades but will focus on:
        -   Periodically re-checking connectivity and API status.
        -   Attempting to retrieve open orders and positions to reconcile its internal state with the exchange.
        -   Logging all reconciliation attempts and their outcomes.
    3.  **Notify Dashboard Agent**: A critical alert will be sent to the Dashboard Agent, indicating the systemic issue and the reconciliation state.
-   **Non-Fatal vs. Fatal Errors**: A clear distinction will be made between non-fatal errors (e.g., nonce errors, temporary rate limits, which trigger key quarantine and retry) and fatal errors (e.g., invalid credentials, which disable a key).

## 6. Communication with Dashboard Agent

The API management framework will provide detailed status updates to the Dashboard Agent, including:

-   **API Status**: A display showing the status of each key in the pool (e.g., `Key 1: ACTIVE`, `Key 2: ACTIVE`, `Key 3: QUARANTINED`).
-   **Rate Limit Status**: Current rate limit usage and remaining capacity.
-   **Error Notifications**: Alerts for key quarantines, disabled keys, and systemic API issues.

This comprehensive API management framework ensures that the Risk & Execution Agent can maintain robust and reliable communication with the Kraken exchange, even under challenging network conditions or during periods of high trading activity.

