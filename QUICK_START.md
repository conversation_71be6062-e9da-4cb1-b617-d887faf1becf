# 🚀 Quick Start Guide - Kraken Live Trading System

## ⚠️ **IMPORTANT: LIVE TRADING ONLY - REAL MONEY AT RISK** ⚠️

This system trades with **REAL MONEY** using your Kraken API keys. There is no sandbox or paper trading mode.

## 📋 Prerequisites

Before you start, make sure you have:

1. **Python 3.8+** installed
2. **5 Kraken API keys** with trading permissions
3. **Real money** in your Kraken account
4. **Understanding of trading risks**

## 🎯 Step-by-Step Setup

### Step 1: Get Your API Keys
1. Go to [Kraken API Settings](https://www.kraken.com/u/security/api)
2. Create **5 API keys** with the following permissions:
   - ✅ Query Funds
   - ✅ Query Open Orders & Trades
   - ✅ Query Closed Orders & Trades
   - ✅ Create & Modify Orders
   - ✅ Cancel Orders
3. **Save all 5 key pairs** securely

### Step 2: Launch the System (Windows)
```batch
# Double-click or run:
launch_trading_system.bat
```

### Step 3: Configure Your Settings
1. Choose option **1** (Configure Trading System)
2. Enter your **5 API keys and secrets**
3. Set your **trading parameters**:
   - Operating mode (trend_following or scalping)
   - Trading pairs (e.g., XBT/USD, ETH/USD)
   - Initial portfolio value
4. Configure **risk management**:
   - Maximum drawdown (e.g., 0.50 = 50%)
   - Position size limits
   - Stop loss and take profit percentages

### Step 4: Test the System
1. Choose option **4** (Run System Tests)
2. Verify all tests pass
3. Check configuration is valid

### Step 5: Start Live Trading
1. Choose option **2** (Launch Live Trading System)
2. **FINAL WARNING**: Confirm you want to trade with real money
3. Monitor the live dashboard

## 🖥️ Dashboard Options

### Full Trading System
- Runs all agents (market data, strategy, risk, dashboard)
- Places real trades automatically
- Full system monitoring

### Dashboard Only
- Choose option **3** for monitoring only
- View system status without trading
- Good for monitoring existing positions

## ⚙️ Manual Setup (Advanced)

If you prefer manual setup:

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Configure system
python trading_configurator.py

# 3. Test system
python test_system.py

# 4. Start trading
python main.py
```

## 📊 What You'll See

The live dashboard shows:
- **Portfolio Summary**: Total equity, P&L, drawdown
- **Trading Performance**: Win rate, trade statistics
- **API Key Status**: Active/quarantined keys
- **Open Positions**: Current trades and P&L
- **Recent Orders**: Latest trading activity
- **System Logs**: Real-time system messages

## 🛑 Emergency Stop

To stop the system safely:
- Press **Ctrl+C** in the terminal
- The system will cancel pending orders
- Positions may remain open (manual close required)

## ⚠️ Risk Warnings

### Before You Start
- **Understand the risks**: Algorithmic trading can result in significant losses
- **Start small**: Use only money you can afford to lose
- **Monitor actively**: Don't leave the system unattended for long periods
- **Test thoroughly**: Make sure you understand how the system works

### Risk Management Features
- **Circuit Breakers**: Automatic halt on excessive losses
- **Position Limits**: Maximum position size controls
- **Drawdown Limits**: Portfolio protection mechanisms
- **API Rotation**: Automatic failover between keys

## 🔧 Troubleshooting

### Common Issues

**"Configuration error"**
- Run `python trading_configurator.py` to set up your API keys

**"Import error"**
- Run `pip install -r requirements.txt` to install dependencies

**"API key error"**
- Check your API keys have trading permissions
- Verify keys are correctly entered in .env file

**"Connection error"**
- Check your internet connection
- Verify Kraken API is accessible

### Getting Help
1. Check the logs in the `logs/` directory
2. Run system tests: `python test_system.py`
3. Review configuration: Check your .env file
4. Restart the system: Close and reopen

## 📁 Important Files

- **`.env`**: Your API keys and configuration (keep secure!)
- **`logs/`**: System log files for debugging
- **`README.md`**: Complete documentation
- **`requirements.txt`**: Required Python packages

## 🚨 Security Notes

- **Never share your .env file** - it contains your API keys
- **Keep API keys secure** - treat them like passwords
- **Regular backups** - backup your configuration
- **Monitor account** - check your Kraken account regularly

## 📞 Support

If you need help:
1. Check this guide and README.md
2. Review system logs for error messages
3. Test with `python test_system.py`
4. Verify your API key permissions on Kraken

---

## 🎯 Ready to Trade?

1. ✅ API keys configured
2. ✅ System tested
3. ✅ Risks understood
4. ✅ Monitoring plan ready

**Run**: `launch_trading_system.bat` and choose option 2!

---

**Remember**: This system trades with REAL MONEY. Always understand the risks and never invest more than you can afford to lose! 🚀📈
