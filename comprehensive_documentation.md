# Kraken Multi-Agent Cryptocurrency Trading System - Comprehensive Documentation

This document provides comprehensive documentation for the Kraken Multi-Agent Cryptocurrency Trading System, covering its architecture, design, implementation details, and operational phases. The system is designed for high-frequency, risk-managed algorithmic trading on the Kraken exchange, featuring user-selectable operational modes and a resilient API management framework.

## Table of Contents

1.  [Project Overview](#1-project-overview)
2.  [System Architecture Overview](#2-system-architecture-overview)
3.  [Market Data Agent Design](#3-market-data-agent-design)
4.  [Strategy & Signal Agent Design](#4-strategy--signal-agent-design)
5.  [Risk & Execution Agent Design (Core Logic)](#5-risk--execution-agent-design-core-logic)
6.  [Risk & Execution Agent Design (API Management)](#6-risk--execution-agent-design-api-management)
7.  [Dashboard Agent Design](#7-dashboard-agent-design)
8.  [Backtesting Environment Design](#8-backtesting-environment-design)
9.  [Paper Trading Environment Design](#9-paper-trading-environment-design)
10. [Live Deployment Preparation](#10-live-deployment-preparation)





## 1. Project Overview

This project aims to develop a modular, multi-agent cryptocurrency trading system for the Kraken exchange. The system is designed for high-frequency, risk-managed algorithmic trading with user-selectable operational modes and a resilient API management framework.

### Core Components:

-   **Market Data Agent**: Handles real-time market data ingestion from Kraken WebSockets.
-   **Strategy & Signal Agent**: Generates trading signals based on selected operating modes (Trend-Following or Scalping).
-   **Risk & Execution Agent**: Manages order placement, risk checks, and intelligent API key rotation.
-   **Dashboard Agent**: Provides a real-time command-line interface for system monitoring.

### Key Features:

-   Multi-agent architecture for concurrent operation.
-   User-selectable trading modes with distinct strategies and risk profiles.
-   Robust risk management protocols, including master circuit breaker and volatility halt.
-   Intelligent API key management with rate-limit awareness.
-   Real-time CLI dashboard for comprehensive system visibility.

### Phased Implementation:

1.  **Backtesting**: Rigorous testing on historical data.
2.  **Paper Trading**: Deployment in a simulated environment with real Kraken API sandbox.
3.  **Live Deployment**: Live trading with capital after successful completion of prior phases.




## 2. System Architecture Overview

The Kraken Multi-Agent Cryptocurrency Trading System is designed with a modular architecture, promoting scalability, maintainability, and clear separation of concerns. The system comprises four main agents, each responsible for a specific set of functionalities, communicating through well-defined interfaces.

### High-Level Diagram

```mermaid
graph TD
    User --> DashboardAgent
    KrakenAPI -- WebSocket/REST --> MarketDataAgent
    MarketDataAgent --> StrategySignalAgent
    StrategySignalAgent --> RiskExecutionAgent
    RiskExecutionAgent -- Orders/API Calls --> KrakenAPI
    RiskExecutionAgent -- Status/Logs --> DashboardAgent
```

### Agent Responsibilities:

### 1. Market Data Agent
- **Role**: Data acquisition and distribution.
- **Inputs**: Real-time Level 2 order book and OHLCV data from Kraken WebSockets.
- **Outputs**: Cleaned, standardized market data streams to other agents.
- **Key Considerations**: Prioritizes WebSocket for low-latency, efficient data handling, and robust error recovery for data feeds.

### 2. Strategy & Signal Agent
- **Role**: Algorithmic strategy execution and signal generation.
- **Inputs**: Market data from Market Data Agent.
- **Outputs**: Non-discretionary trading signals (entry, take-profit, stop-loss levels) to Risk & Execution Agent.
- **Key Considerations**: Supports user-selectable operating modes (Trend-Following, Scalping), each with distinct strategies and risk parameters. Focuses on precise signal generation based on technical indicators and market conditions.

### 3. Risk & Execution Agent
- **Role**: Centralized control for trade execution, risk management, and API interaction.
- **Inputs**: Trading signals from Strategy & Signal Agent.
- **Outputs**: Order placement/cancellation requests to Kraken API, status updates/logs to Dashboard Agent.
- **Key Considerations**: Implements critical pre-trade checks (position sizing, portfolio drawdown limits), intelligent API key management (rotation, quarantine), and rate-limit awareness. Acts as the sole interface with the Kraken exchange for order management.

### 4. Dashboard Agent
- **Role**: Real-time system monitoring and user interface.
- **Inputs**: System status, trade logs, and agent-specific information from other agents.
- **Outputs**: Continuously updated command-line interface (CLI) display for the user.
- **Key Considerations**: Operates in a separate thread to ensure non-interruption of trading operations. Provides clear visibility into system health, trading performance, and API status.

### Communication Flow:

-   **Market Data Flow**: Unidirectional from Market Data Agent to Strategy & Signal Agent.
-   **Signal Flow**: Unidirectional from Strategy & Signal Agent to Risk & Execution Agent.
-   **Execution & Status Flow**: Bidirectional between Risk & Execution Agent and Kraken API; unidirectional from Risk & Execution Agent to Dashboard Agent.
-   **User Interaction**: Primarily through the Dashboard Agent for monitoring and initial mode selection.

### Data Storage:

-   **Ephemeral Data**: Real-time market data and intermediate signals are processed in-memory for speed.
-   **Persistent Data**: Trade history, configuration settings, and critical logs will be stored persistently (e.g., in flat files or a lightweight database) for post-analysis and system recovery.

### Error Handling & Resilience:

-   Each agent will implement robust error handling for its specific domain.
-   Centralized error logging and alerting mechanisms.
-   Critical risk management protocols (Master Circuit Breaker, Volatility Halt, API/Exchange Error Handling) are primarily managed by the Risk & Execution Agent to ensure system stability and capital protection.




## 3. Market Data Agent Design



# Market Data Agent: Design for Data Ingestion and Streaming

## 1. Overview

The Market Data Agent (MDA) is the foundational component of the Kraken Multi-Agent Cryptocurrency Trading System, responsible for establishing and maintaining real-time data feeds from the Kraken exchange. Its primary objective is to ingest raw market data, process it into a standardized format, and efficiently stream it to other agents within the system, particularly the Strategy & Signal Agent. Given the high-frequency nature of the trading system, the MDA prioritizes low-latency data acquisition and robust handling of WebSocket connections.

## 2. Data Sources and Prioritization

The Kraken exchange offers various methods for accessing market data, including REST API endpoints and WebSocket feeds. For this high-frequency trading system, WebSockets are the preferred and mandatory method for real-time data ingestion due to their persistent, low-latency, and push-based nature. REST API calls will be used sparingly, primarily for initial data synchronization (e.g., fetching historical data for backtesting or initial order book snapshots) or in scenarios where WebSocket connectivity is temporarily unavailable.

- **Primary Data Source**: Kraken WebSocket API
    - **Level 2 Order Book Data**: Provides real-time updates on bids and asks, crucial for understanding market depth and liquidity. The MDA will subscribe to the `book` channel for selected trading pairs.
    - **Candlestick (OHLCV) Data**: Delivers aggregated price data over specific timeframes (e.g., 1-minute, 5-minute, 4-hour, daily). The MDA will subscribe to the `ohlc` channel for relevant timeframes and trading pairs.

- **Secondary Data Source (Limited Use)**: Kraken REST API
    - **Initial Order Book Snapshot**: Used to reconstruct the full order book state upon connection or reconnection to the WebSocket feed.
    - **Historical Data**: For backtesting purposes, historical OHLCV data will be fetched via the REST API.

## 3. Data Ingestion Mechanism (WebSocket)

The MDA will establish and manage persistent WebSocket connections to Kraken. The ingestion mechanism will involve the following steps:

1.  **Connection Establishment**: Upon initialization, the MDA will attempt to establish WebSocket connections to the Kraken public and private WebSocket endpoints (if private data, like user trades, is later required). Robust error handling and reconnection logic will be implemented to ensure continuous data flow.
2.  **Subscription Management**: The MDA will dynamically subscribe to the necessary channels (e.g., `book`, `ohlc`) for specified trading pairs and timeframes. This will be configurable, allowing users to select the assets and data types relevant to their chosen operating mode.
3.  **Message Parsing**: Raw JSON messages received from the WebSocket will be parsed and validated. The MDA will be responsible for deserializing the JSON payloads into structured data objects.
4.  **Order Book Reconstruction**: For Level 2 order book data, the MDA will maintain an in-memory representation of the order book. Incremental updates received via WebSocket (add, remove, change) will be applied to this local order book. A mechanism to request full order book snapshots via REST API will be in place to handle desynchronization or initial state population.
5.  **Data Normalization**: All ingested data, regardless of its source (WebSocket or REST), will be normalized into a consistent internal data format. This ensures that downstream agents receive data in a predictable and easily consumable structure.

## 4. Data Streaming Mechanism (Inter-Agent Communication)

The MDA will act as a central data hub, streaming processed market data to other agents. Given the real-time and continuous nature of market data, an efficient inter-process communication (IPC) mechanism is crucial. Python\'s `multiprocessing` module, specifically `Queue` or `Pipe` objects, are suitable candidates for this purpose, allowing agents to run in separate processes while maintaining efficient data exchange.

1.  **Data Queues/Pipes**: The MDA will maintain separate output queues or pipes for each type of data (e.g., order book updates, OHLCV candles) or for each subscribing agent. This allows for asynchronous data delivery and prevents blocking.
2.  **Publisher-Subscriber Model (Conceptual)**: While not a formal message broker, the MDA will conceptually operate as a publisher, pushing data to its subscribers (other agents). Agents will register their interest in specific data streams upon their initialization.
3.  **Data Serialization**: Before placing data onto the queues/pipes, data objects will be serialized (e.g., using `pickle` or `json` for more complex objects) to ensure safe and efficient transmission across process boundaries.
4.  **Flow Control**: Mechanisms to handle potential backpressure (e.g., if a consuming agent is slower than the MDA\'s data output) will be considered to prevent memory exhaustion. This might involve bounded queues or discarding older data if necessary.

## 5. Error Handling and Resilience

-   **WebSocket Reconnection**: Automatic reconnection logic with exponential backoff will be implemented for dropped WebSocket connections.
-   **Data Validation**: Incoming data will be validated for integrity and completeness. Corrupted or malformed messages will be logged and discarded.
-   **Rate Limit Awareness (Passive)**: While the Risk & Execution Agent will actively manage API rate limits for outgoing requests, the MDA will passively monitor its own data request frequency to avoid being rate-limited by Kraken for data subscriptions.
-   **Logging**: Comprehensive logging will be implemented to record connection status, subscription events, data ingestion rates, and any errors encountered.

## 6. Configuration

The MDA\'s behavior will be configurable via a dedicated configuration file (e.g., `config/market_data_agent.yaml` or `.env` variables). Key configurable parameters will include:

-   Kraken WebSocket URL
-   List of trading pairs to subscribe to (e.g., `XBT/USD`, `ETH/USD`)
-   List of OHLCV timeframes to subscribe to (e.g., `1`, `5`, `240`, `1440` minutes)
-   Logging level

This design ensures that the Market Data Agent is robust, efficient, and provides a reliable data foundation for the entire trading system.




## 4. Strategy & Signal Agent Design



# Strategy & Signal Agent: Design for Strategy Module Interface and Signal Generation

## 1. Overview

The Strategy & Signal Agent (SSA) is a critical component of the Kraken Multi-Agent Cryptocurrency Trading System, responsible for ingesting processed market data from the Market Data Agent, applying defined trading strategies, and generating precise, non-discretionary trading signals. These signals, which include entry, take-profit, and stop-loss levels, are then passed to the Risk & Execution Agent for order placement. The SSA is designed to be modular, allowing for easy integration of new trading strategies and user-selectable operational modes.

## 2. Responsibilities

The primary responsibilities of the Strategy & Signal Agent include:

-   **Data Ingestion**: Receiving real-time Level 2 order book and OHLCV data streams from the Market Data Agent.
-   **Strategy Execution**: Applying the logic of the currently selected trading strategy (Trend-Following or Scalping).
-   **Signal Generation**: Producing clear, actionable trading signals (entry, take-profit, stop-loss) based on strategy rules and market conditions.
-   **Risk Parameter Integration**: Incorporating strategy-specific risk parameters (e.g., default risk percentage per trade) into signal generation.
-   **Filtering and Confirmation**: Utilizing additional indicators (e.g., RSI for Trend-Following) to confirm signals and reduce false positives.

## 3. Strategy Module Interface Design

To ensure modularity and extensibility, each trading strategy will adhere to a common interface. This interface will define the methods and properties that every strategy module must implement, allowing the SSA to dynamically load and execute different strategies based on user selection.

### `BaseStrategy` Abstract Class

All concrete strategy implementations will inherit from a `BaseStrategy` abstract class, which will define the common interface. This ensures that all strategies provide the necessary methods for the SSA to interact with them.

```python
from abc import ABC, abstractmethod

class BaseStrategy(ABC):
    def __init__(self, pair: str, timeframe: str, risk_percentage: float):
        self.pair = pair
        self.timeframe = timeframe
        self.risk_percentage = risk_percentage
        self.signals = [] # To store generated signals

    @abstractmethod
    async def on_ohlcv_update(self, ohlcv_data: dict):
        """
        Processes new OHLCV data and generates trading signals.
        """
        pass

    @abstractmethod
    async def on_order_book_update(self, order_book_data: dict):
        """
        Processes new order book data (if relevant to the strategy).
        """
        pass

    @abstractmethod
    def get_signals(self) -> list:
        """
        Returns a list of generated trading signals.
        Each signal should be a dictionary containing:
        {
            \"type\": \"entry\" | \"take_profit\" | \"stop_loss\",
            \"pair\": str,
            \"price\": float,
            \"timestamp\": float,
            \"strategy_id\": str,
            \"risk_percentage\": float # Risk for this specific trade
        }
        """
        pass

    @abstractmethod
    def get_required_data_streams(self) -> dict:
        """
        Returns a dictionary specifying the data streams required by the strategy.
        Example: {\"ohlc\": [(\"XBT/USD\", \"1\"), (\"XBT/USD\", \"5\")], \"book\": [(\"XBT/USD\")]}
        """
        pass

    def clear_signals(self):
        """
        Clears the list of generated signals after they have been processed.
        """
        self.signals = []

```

### Concrete Strategy Implementations

Each specific trading strategy (e.g., `TrendFollowingStrategy`, `ScalpingStrategy`) will be a concrete implementation of the `BaseStrategy` class. They will implement the `on_ohlcv_update`, `on_order_book_update`, `get_signals`, and `get_required_data_streams` methods according to their specific logic.

## 4. Signal Generation and Structure

Trading signals generated by the SSA will be non-discretionary and will contain all necessary information for the Risk & Execution Agent to place an order. A signal will be represented as a dictionary with the following structure:

| Field             | Type    | Description                                                                 |
| :---------------- | :------ | :-------------------------------------------------------------------------- |
| `type`            | `str`   | Type of signal: `entry`, `take_profit`, or `stop_loss`.                     |
| `pair`            | `str`   | Trading pair for the signal (e.g., `XBT/USD`).                              |
| `price`           | `float` | The target price for the signal.                                            |
| `timestamp`       | `float` | Unix timestamp when the signal was generated.                               |
| `strategy_id`     | `str`   | Unique identifier for the strategy that generated the signal.               |
| `risk_percentage` | `float` | The percentage of total portfolio equity to risk on this specific trade.    |

## 5. Data Flow within SSA

1.  **Initialization**: The SSA will be initialized with the user-selected `Operating_Mode` and associated configuration (e.g., trading pairs, timeframes, default risk).
2.  **Strategy Instantiation**: Based on the selected mode, the appropriate concrete strategy class will be instantiated.
3.  **Data Consumption**: The SSA will continuously consume data from the Market Data Agent\`s queues. When new OHLCV or order book data arrives, it will be passed to the `on_ohlcv_update` or `on_order_book_update` method of the active strategy instance.
4.  **Signal Retrieval**: Periodically, or upon specific events (e.g., new candle close), the SSA will call the `get_signals` method of the active strategy. Any generated signals will be retrieved.
5.  **Signal Forwarding**: Retrieved signals will be immediately forwarded to the Risk & Execution Agent\`s input queue.
6.  **Signal Clearing**: After forwarding, the `clear_signals` method of the strategy will be called to prevent duplicate processing.

## 6. User-Selectable Operating Modes

The SSA will support two primary operating modes, each corresponding to a distinct trading strategy:

### [A] Trend-Following Mode (Long-Term Growth)

-   **Timeframe**: Primarily 4-hour and Daily charts.
-   **Strategy**: Identifies established trends using a 50/200 Exponential Moving Average (EMA) crossover system. An entry signal is generated only in the direction of the trend on a confirmed pullback, with the Relative Strength Index (RSI) used as a confirmation filter.
-   **Default Risk**: No single trade shall risk more than 2% of total portfolio equity.

### [B] Scalping Mode (Short-Term Gains)

-   **Timeframe**: Primarily 1-minute and 5-minute charts.
-   **Strategy**: A high-frequency, mean-reversion strategy using Bollinger Bands and order flow analysis. Entry signals are generated on sharp deviations from the mean, expecting a quick reversion.
-   **Default Risk**: No single trade shall risk more than 0.5% of total portfolio equity.

## 7. Configuration

The SSA will be configurable, allowing for dynamic selection of operating modes and adjustment of strategy parameters. This will likely involve a configuration file (e.g., `config/strategy_agent.yaml`) or environment variables.

Key configurable parameters will include:

-   `operating_mode`: (`trend_following` or `scalping`)
-   `trading_pairs`: List of pairs the SSA should monitor.
-   `trend_following_params`:
    -   `ema_short_period`: 50
    -   `ema_long_period`: 200
    -   `rsi_period`: 14
    -   `rsi_overbought`: 70
    -   `rsi_oversold`: 30
    -   `default_risk_percentage`: 0.02 (2%)
-   `scalping_params`:
    -   `bollinger_period`: 20
    -   `bollinger_std_dev`: 2
    -   `default_risk_percentage`: 0.005 (0.5%)

This design provides a clear roadmap for implementing a flexible and robust Strategy & Signal Agent capable of supporting multiple trading strategies and adapting to user requirements.




## 5. Risk & Execution Agent Design (Core Logic)



# Risk & Execution Agent: Design for Order Placement and Management Module

## 1. Overview

The Risk & Execution Agent (REA) serves as the central control unit of the Kraken Multi-Agent Cryptocurrency Trading System. Its primary responsibilities include receiving trading signals from the Strategy & Signal Agent, performing critical pre-trade risk checks, managing API interactions with the Kraken exchange, and ensuring the safe and efficient execution of trades. This document focuses on the design of the order placement and management module, which is at the core of the REA\'s operational capabilities.

## 2. Core Responsibilities of the Order Placement and Management Module

The order placement and management module within the REA is responsible for:

-   **Signal Ingestion**: Receiving and processing trading signals (entry, take-profit, stop-loss) from the Strategy & Signal Agent.
-   **Pre-Trade Risk Verification**: Implementing and enforcing critical risk management rules before any order is placed. This includes position sizing and overall portfolio drawdown limits.
-   **Order Construction**: Translating validated signals into executable Kraken API order parameters.
-   **Order Placement**: Interacting with the Kraken API to submit new orders.
-   **Order Tracking and State Management**: Maintaining an accurate, real-time record of all open orders and their current status (e.g., pending, open, filled, canceled).
-   **Order Modification/Cancellation**: Handling requests to modify or cancel existing orders based on risk management protocols or strategy adjustments.
-   **Position Management**: Tracking current open positions, including entry price, quantity, and associated profit/loss.
-   **Communication with Dashboard Agent**: Providing real-time updates on order status, trade executions, and position changes to the Dashboard Agent.

## 3. Order Flow and Processing

The following steps outline the typical flow of an order through the REA\'s order placement and management module:

1.  **Signal Reception**: The REA continuously monitors a dedicated input queue for new trading signals from the Strategy & Signal Agent. Each signal contains details such as `type` (entry, take_profit, stop_loss), `pair`, `price`, `timestamp`, `strategy_id`, and `risk_percentage`.

2.  **Pre-Trade Risk Checks (Sequential and Critical)**:
    *   **Position Sizing Rule Verification**: Before an entry order is placed, the module calculates the appropriate trade size based on the `risk_percentage` provided in the signal and the current total portfolio equity. It ensures that the calculated trade size adheres to predefined position sizing rules (e.g., maximum capital allocated per trade, minimum trade size).
    *   **Master Max Portfolio Drawdown Limit Verification**: The module checks if placing the proposed trade would cause the potential maximum drawdown of the entire portfolio to exceed the `max_portfolio_drawdown` limit defined by the user. If the potential loss from this trade, combined with existing open positions, would breach this limit, the trade is rejected.

3.  **Order Construction**: If all risk checks pass, the module constructs the Kraken API order request. This involves:
    *   Determining the order type (e.g., `limit`, `market`). For entry signals, it will likely be a limit order at the signaled price or a market order if immediate execution is prioritized.
    *   Calculating the exact quantity to trade based on the position sizing. This will involve converting the calculated risk amount into a quantity of the base currency.
    *   Setting up associated take-profit and stop-loss orders (often as OCO - One-Cancels-the-Other - orders if supported by Kraken, or managed client-side).

4.  **Order Placement**: The constructed order is then sent to the Kraken API via the API management framework (which handles key rotation and rate limiting). The module will wait for the API response to confirm order submission.

5.  **Order Tracking and State Management**: Upon successful submission, the order\'s details (Kraken order ID, type, status, quantity, price) are stored in an in-memory order book. This internal state is continuously updated based on WebSocket private feed updates (e.g., `ownTrades`, `openOrders`) or periodic REST API queries.

6.  **Position Management**: When an entry order is fully filled, a new position is opened. The module tracks all open positions, including the average entry price, current quantity, and real-time unrealized profit/loss. When a take-profit or stop-loss order is filled, the corresponding position is closed.

7.  **Order Modification/Cancellation**: The module will expose methods to modify or cancel orders. This is crucial for implementing risk management actions like the 


Master Circuit Breaker or Volatility Halt. These actions will trigger API calls to cancel open orders or close positions.

## 4. Risk Management Integration

### Position Sizing Rule

-   **Rule**: No single trade shall risk more than a user-defined percentage of total portfolio equity (e.g., 2% for Trend-Following, 0.5% for Scalping). This percentage is provided by the Strategy & Signal Agent with each signal.
-   **Calculation**: The REA will calculate the maximum allowable loss for a trade based on the `risk_percentage` and the current `total_equity`. This loss amount is then used to determine the position size (quantity) given the entry price and the stop-loss level. The formula for calculating position size will be:

    ```
    Position Size (Units) = (Total Equity * Risk Percentage) / (Entry Price - Stop Loss Price)
    ```

    If the calculated `Position Size` is less than Kraken\'s minimum order size for the `pair`, the trade will be rejected. Similarly, if it exceeds a predefined maximum position size, it will be capped or rejected.

### Master Max Portfolio Drawdown Limit

-   **Rule**: The user must define a `max_portfolio_drawdown` percentage (e.g., 50%). If the total account equity drops to this level, the REA must trigger a \"Total Halt\": cancel all orders, close all positions, and cease all trading.
-   **Monitoring**: The REA will continuously monitor the `TOTAL EQUITY` (which includes realized and unrealized P/L). This requires fetching account balance information from Kraken (via REST API or private WebSocket if available) and combining it with the unrealized P/L of open positions.
-   **Trigger**: When `Current Equity <= Initial Equity * (1 - max_portfolio_drawdown)`, the \"Total Halt\" is activated.
-   **Action (Total Halt)**:
    1.  **Cancel All Open Orders**: Immediately send API calls to cancel all active orders on Kraken.
    2.  **Close All Positions**: Execute market orders to close all open long and short positions.
    3.  **Cease Trading**: Set an internal flag to `trading_halted = True`, preventing any new signals from being processed or orders from being placed.
    4.  **Notify Dashboard Agent**: Send a critical alert to the Dashboard Agent for immediate user notification.

### Volatility Halt

-   **Rule**: If the Market Data Agent detects a price change of more than 8% in any single monitored asset within a 5-minute period, the Risk & Execution Agent will enter a \"Cool-Down\" state for 15 minutes, rejecting new signals.
-   **Detection**: The MDA will be responsible for detecting significant price changes and signaling this event to the REA. The REA will then verify the condition.
-   **Action (Cool-Down)**:
    1.  **Reject New Signals**: Temporarily set an internal flag to `cool_down_active = True` for 15 minutes, causing the REA to ignore any incoming signals from the Strategy & Signal Agent.
    2.  **Maintain Existing Orders/Positions**: Unlike the \"Total Halt,\" existing open orders and positions are not immediately affected, but no new trades will be initiated.
    3.  **Notify Dashboard Agent**: Inform the Dashboard Agent about the volatility halt and its duration.

## 5. Communication with Dashboard Agent

The REA will communicate critical information to the Dashboard Agent to ensure real-time visibility for the user. This will include:

-   **Order Status Updates**: Confirmation of order placement, fills, cancellations, and rejections.
-   **Position Updates**: Changes in open positions, including new entries, closures, and real-time unrealized P/L.
-   **Risk Management Alerts**: Notifications for triggered circuit breakers, volatility halts, or API errors.
-   **System Logs**: Detailed logs of all actions taken by the REA.

This communication will likely occur via a dedicated `asyncio.Queue` or similar IPC mechanism, ensuring non-blocking updates to the CLI dashboard.

## 6. API Interaction (Brief Overview - Detailed in next section)

The REA will be the sole agent responsible for direct interaction with the Kraken REST API for order placement, cancellation, and account information retrieval. This interaction will be mediated by a robust API management framework that handles:

-   **API Key Pool Management**: Utilizing multiple API keys for redundancy and load balancing.
-   **Intelligent Key Rotation**: Switching keys in a round-robin fashion and quarantining problematic keys.
-   **Rate-Limit Awareness**: Actively tracking API call costs and pausing requests to avoid exceeding Kraken\'s rate limits.

This separation of concerns ensures that the core logic of order placement and risk management is decoupled from the complexities of API interaction, which will be detailed in the next design document.




## 6. Risk & Execution Agent Design (API Management)



# Risk & Execution Agent: Design for Resilient API Management Framework

## 1. Overview

The Risk & Execution Agent (REA) is the sole component of the trading system that directly interacts with the Kraken API for order placement, cancellation, and account information retrieval. To ensure maximum uptime, efficiency, and compliance with exchange rules, the REA incorporates a resilient API management framework. This framework is designed to handle API key pools, intelligently rotate keys, and manage request frequency to stay within Kraken\`s rate limits, thereby preventing temporary IP bans and ensuring continuous operation.

## 2. API Key Pool Management

The system will be initialized with a list of one or more user-provided Kraken API keys. This pool provides redundancy and allows for intelligent distribution of API calls.

-   **Configuration**: API keys (key-secret pairs) will be loaded securely, preferably from environment variables or a dedicated configuration file that is not committed to version control.
-   **Storage**: The REA will maintain an in-memory list of `KrakenAPIClient` instances, each initialized with a unique API key from the pool.
-   **Status Tracking**: Each key in the pool will have an associated status (e.g., `ACTIVE`, `QUARANTINED`, `DISABLED`).
    -   `ACTIVE`: The key is fully operational and available for use.
    -   `QUARANTINED`: The key has encountered a non-fatal error (e.g., nonce error, temporary rate limit hit) and is temporarily taken out of rotation for a defined period.
    -   `DISABLED`: The key has encountered a fatal error or repeated non-fatal errors, indicating a persistent issue, and is permanently removed from rotation until manual intervention.

## 3. Intelligent Key Rotation

For each new order or API action, the REA will select the next available API key from the pool in a round-robin fashion. This distributes the load across multiple keys and helps mitigate individual key-specific issues.

-   **Round-Robin Selection**: A simple counter will be used to cycle through the `ACTIVE` keys in the pool.
-   **Error Handling and Quarantine**: If an API call using a selected key returns a nonce error or another non-fatal API error (e.g., `EAPI:RateLimit` that is not account-wide), the following actions will be taken:
    1.  **Temporarily Quarantine Key**: The problematic key will be marked as `QUARANTINED` and a timestamp will be recorded, indicating when it can be re-activated.
    2.  **Immediate Retry**: The REA will immediately retry the failed API action with the *next available* `ACTIVE` key in the pool. This ensures that critical operations are not delayed by transient key issues.
    3.  **Re-activation Logic**: Quarantined keys will be automatically re-activated after a predefined cool-down period (e.g., 5 minutes). If a key repeatedly enters the `QUARANTINED` state within a short timeframe, it might be escalated to `DISABLED` status.
-   **Fatal Error Handling**: If an API call returns a fatal error (e.g., invalid API key, insufficient permissions), the key will be marked as `DISABLED` and an alert will be sent to the Dashboard Agent. The system will continue operating with the remaining `ACTIVE` keys.

## 4. Rate-Limit Awareness

Kraken imposes account-wide rate limits on API requests. Exceeding these limits can lead to temporary IP bans, severely impacting trading operations. The REA must actively track the API call cost associated with its actions and intelligently manage the request frequency to stay below these limits.

-   **Kraken Rate Limit Mechanism**: Kraken uses a 


cost-based rate limit system, where each API endpoint has an associated \"cost.\" The total cost of requests within a rolling window (e.g., 20-30 seconds) must not exceed a certain threshold. The current rate limit status is often returned in API response headers.

-   **Cost Tracking**: The REA will maintain an internal counter for the total API call cost within the rolling window. Each time an API call is made, its known cost will be added to this counter.
-   **Dynamic Delay Mechanism**: Before making an API call, the REA will check the current cost counter against the allowed limit. If the projected cost of the next call would exceed the limit, the REA will introduce a dynamic delay (e.g., `asyncio.sleep()`) to wait until the rolling window clears enough cost to accommodate the new request. This proactive approach prevents hitting the rate limit.
-   **Response Header Monitoring**: The REA will parse API response headers (e.g., `X-Kraken-RateLimit-Remaining`, `X-Kraken-RateLimit-Reset`) to get real-time feedback on rate limit status. This information will be used to adjust the internal cost tracking and dynamic delay mechanism for optimal performance.
-   **Account-Wide vs. Key-Specific Limits**: It\`s crucial to distinguish between account-wide rate limits (which apply to all keys under the same account) and potential key-specific limits (less common but possible). The primary focus will be on managing the account-wide limit.

## 5. Error Handling and Reconciliation

-   **API/Exchange Error Handling**: If the Risk & Execution Agent exhausts all `ACTIVE` keys in its pool with consecutive failed API calls (e.g., all keys return `EAPI:RateLimit` or a similar critical error), it will assume a systemic issue with the exchange or connectivity.
    1.  **Halt Placing New Trades**: The REA will set `trading_halted = True` and enter a `reconciliation_state`.
    2.  **Enter Reconciliation State**: In this state, the REA will cease attempting new trades but will focus on:
        -   Periodically re-checking connectivity and API status.
        -   Attempting to retrieve open orders and positions to reconcile its internal state with the exchange.
        -   Logging all reconciliation attempts and their outcomes.
    3.  **Notify Dashboard Agent**: A critical alert will be sent to the Dashboard Agent, indicating the systemic issue and the reconciliation state.
-   **Non-Fatal vs. Fatal Errors**: A clear distinction will be made between non-fatal errors (e.g., nonce errors, temporary rate limits, which trigger key quarantine and retry) and fatal errors (e.g., invalid credentials, which disable a key).

## 6. Communication with Dashboard Agent

The API management framework will provide detailed status updates to the Dashboard Agent, including:

-   **API Status**: A display showing the status of each key in the pool (e.g., `Key 1: ACTIVE`, `Key 2: ACTIVE`, `Key 3: QUARANTINED`).
-   **Rate Limit Status**: Current rate limit usage and remaining capacity.
-   **Error Notifications**: Alerts for key quarantines, disabled keys, and systemic API issues.

This comprehensive API management framework ensures that the Risk & Execution Agent can maintain robust and reliable communication with the Kraken exchange, even under challenging network conditions or during periods of high trading activity.




## 7. Dashboard Agent Design



# Dashboard Agent: Design for Command-Line Interface (CLI) Display

## 1. Overview

The Dashboard Agent (DA) is responsible for providing a real-time, continuously updated command-line interface (CLI) display of the Kraken Multi-Agent Cryptocurrency Trading System\`s operational status, performance metrics, and critical alerts. Its primary goal is to ensure the user has constant visibility into the system\`s health and trading activities without interrupting the core trading operations. The DA runs in a separate thread or process to maintain responsiveness and avoid blocking other agents.

## 2. Core Responsibilities

The Dashboard Agent\`s responsibilities include:

-   **Data Consumption**: Receiving status updates, trade logs, and performance metrics from other agents (primarily the Risk & Execution Agent and potentially the Market Data Agent).
-   **Real-time Display**: Presenting this information in a clear, concise, and continuously updated CLI format.
-   **User Visibility**: Ensuring the user has immediate access to critical information such as operating mode, equity, P/L, drawdown, API status, open positions, and system logs.
-   **Non-Blocking Operation**: Operating asynchronously to prevent any impact on the low-latency trading operations.

## 3. CLI Dashboard Layout and Components

The CLI dashboard will be designed for readability and quick information assimilation. It will be structured into several distinct sections, each dedicated to a specific category of information. The display will be refreshed periodically (e.g., every 1-2 seconds) to reflect the latest data.

### Proposed Layout Structure

```
================================================================================
KRAKEN MULTI-AGENT TRADING SYSTEM - REAL-TIME DASHBOARD
================================================================================

OPERATING MODE: [Trend-Following / Scalping] | PAIR: [XBT/USD]

--------------------------------------------------------------------------------
PORTFOLIO SUMMARY
--------------------------------------------------------------------------------
TOTAL EQUITY:             [USD X,XXX.XX]
REALIZED P/L (24h):       [USD X.XX / X.XX%]
UNREALIZED P/L:           [USD X.XX / X.XX%]
MAX DRAWDOWN LIMIT:       [X.XX%]
CURRENT DRAWDOWN:         [X.XX%]

--------------------------------------------------------------------------------
API STATUS
--------------------------------------------------------------------------------
Key 1 (XXXXX): ACTIVE     | Last Used: [HH:MM:SS]
Key 2 (YYYYY): QUARANTINED | Until: [HH:MM:SS]
Key 3 (ZZZZZ): DISABLED   | Reason: [Invalid Key]

--------------------------------------------------------------------------------
OPEN POSITIONS
--------------------------------------------------------------------------------
PAIR      | TYPE | VOLUME    | ENTRY PRICE | CURRENT PRICE | UNREALIZED P/L
----------|------|-----------|-------------|---------------|---------------
XBT/USD   | LONG | 0.05000   | 20,000.00   | 20,050.00     | +2.50 (+0.25%)
ETH/USD   | SHORT| 0.10000   | 1,500.00    | 1,490.00      | +1.00 (+0.10%)

--------------------------------------------------------------------------------
SYSTEM LOG
--------------------------------------------------------------------------------
[HH:MM:SS] [INFO] MarketDataAgent: Connected to WebSocket.
[HH:MM:SS] [WARNING] RiskExecutionAgent: VOLATILITY HALT for XBT/USD! Cooling down for 15 minutes.
[HH:MM:SS] [CRITICAL] RiskExecutionAgent: MASTER CIRCUIT BREAKER TRIGGERED! Trading Halted.
[HH:MM:SS] [INFO] StrategySignalAgent: Generated BUY signal for XBT/USD.
[HH:MM:SS] [ERROR] RiskExecutionAgent: Failed to place order: Insufficient funds.
```

### Component Breakdown:

1.  **Header**: Displays the system title and a clear indication of its real-time nature.
2.  **Operating Mode & Pair**: Shows the currently active trading mode (Trend-Following or Scalping) and the primary trading pair(s) being monitored. This provides immediate context to the user.
3.  **Portfolio Summary**: This section provides a high-level overview of the user\`s financial status within the system.
    -   `TOTAL EQUITY`: The current total value of the portfolio, including cash and open positions.
    -   `REALIZED P/L (24h)`: Profit/Loss from closed trades over the last 24 hours, both in absolute currency and percentage terms.
    -   `UNREALIZED P/L`: Current profit/loss from open positions, both in absolute currency and percentage terms.
    -   `MAX DRAWDOWN LIMIT`: The user-defined maximum percentage drawdown allowed for the total portfolio.
    -   `CURRENT DRAWDOWN`: The current percentage drawdown from the peak equity, indicating how far the portfolio has fallen from its highest point.
4.  **API STATUS**: This is a crucial new section providing transparency into the API management framework.
    -   Lists each configured API key (masked for security, e.g., `XXXXX`).
    -   Displays the current `status` of each key (`ACTIVE`, `QUARANTINED`, `DISABLED`).
    -   For `QUARANTINED` keys, it shows the `Until` timestamp when it will be re-activated.
    -   For `DISABLED` keys, it provides a brief `Reason` for its deactivation.
    -   `Last Used`: Timestamp of the last successful API call using that key (optional, but useful for debugging).
5.  **OPEN POSITIONS**: A table detailing all currently open trading positions.
    -   `PAIR`: The trading pair (e.g., XBT/USD).
    -   `TYPE`: `LONG` or `SHORT`.
    -   `VOLUME`: The quantity of the asset held.
    -   `ENTRY PRICE`: The average price at which the position was entered.
    -   `CURRENT PRICE`: The current market price of the asset.
    -   `UNREALIZED P/L`: The current profit or loss for that specific position, both in absolute and percentage terms.
6.  **SYSTEM LOG**: A scrolling log display showing recent system events, warnings, errors, and informational messages from all agents. This provides a chronological record of system activity and helps in diagnosing issues.
    -   Each log entry will include a timestamp and the originating agent.
    -   Different log levels (INFO, WARNING, ERROR, CRITICAL) will be clearly distinguishable.

## 4. Data Flow to Dashboard Agent

The Dashboard Agent will receive updates from other agents via dedicated `asyncio.Queue` objects. This asynchronous communication ensures that the DA does not introduce latency into the critical paths of other agents.

-   **Risk & Execution Agent**: Will push updates related to:
    -   Portfolio equity changes.
    -   Realized and unrealized P/L updates.
    -   Order placement, modification, and cancellation events.
    -   Position changes (open, close, size adjustments).
    -   API key status changes (active, quarantined, disabled).
    -   Rate limit warnings/status.
    -   Circuit breaker and volatility halt triggers.
-   **Market Data Agent**: Could potentially push updates related to:
    -   WebSocket connection status (connected/disconnected).
    -   Significant price movements (for volatility halt detection).
-   **Strategy & Signal Agent**: Could push notifications about:
    -   Strategy activation/deactivation.
    -   Signal generation (for logging purposes, not for direct display of every signal).

## 5. Implementation Considerations

-   **Terminal Manipulation**: Libraries like `curses` or `blessed` can be used for advanced terminal manipulation to create a dynamic, refreshing display without simply printing new lines. This allows for overwriting previous content, creating a true 


interactive dashboard. Alternatively, simpler methods involving clearing the screen and re-printing can be used for basic functionality.
-   **Asynchronous Updates**: The DA will use `asyncio` to consume data from queues and update the display without blocking the main event loop.
-   **Logging Integration**: The system log section will consume messages from a centralized logging handler, allowing all agents to contribute to the displayed log.
-   **Configuration**: The DA will be configurable for display refresh rates, log levels, and potentially which trading pairs to highlight.

This design provides a comprehensive plan for building a user-friendly and informative CLI dashboard for the Kraken Multi-Agent Trading System.




## 8. Backtesting Environment Design



# Backtesting Environment: Design for Setup and Execution

## 1. Overview

Backtesting is a crucial phase in the development of any algorithmic trading system. It involves simulating the performance of a trading strategy using historical market data to evaluate its viability, profitability, and risk characteristics before deploying it in a live or paper trading environment. This document outlines the design for setting up and executing the backtesting environment for the Kraken Multi-Agent Cryptocurrency Trading System.

## 2. Objectives of Backtesting

The primary objectives of the backtesting phase are to:

-   **Validate Strategy Logic**: Confirm that the Trend-Following and Scalping strategies behave as expected under various historical market conditions.
-   **Assess Profitability**: Determine the potential returns of each strategy over a significant historical period.
-   **Quantify Risk**: Measure key risk metrics such as maximum drawdown, volatility, and Sharpe ratio.
-   **Optimize Parameters**: Identify optimal or robust parameters for each strategy.
-   **Identify Edge Cases**: Uncover unexpected behaviors or vulnerabilities in the strategy logic.

## 3. Backtesting Framework Design

The backtesting framework will be a standalone component, separate from the real-time trading agents, to ensure that backtesting results are not influenced by real-time operational constraints. It will simulate the data flow and signal processing of the live system.

### Core Components:

1.  **Historical Data Loader**: Responsible for loading and preparing historical Kraken data.
2.  **Event-Driven Engine**: Simulates the passage of time and dispatches historical market data events to the strategy.
3.  **Strategy Wrapper**: Adapts the `Strategy & Signal Agent`\`s `BaseStrategy` interface to work within the backtesting environment.
4.  **Trade Simulator**: Processes signals from the strategy, simulates order execution (assuming perfect or near-perfect fills), and manages simulated positions and equity.
5.  **Performance Analyzer**: Calculates and reports key performance metrics.

### Data Flow:

```mermaid
graph TD
    HistoricalDataLoader --> EventDrivenEngine
    EventDrivenEngine --> StrategyWrapper
    StrategyWrapper --> TradeSimulator
    TradeSimulator --> PerformanceAnalyzer
    PerformanceAnalyzer --> UserReport
```

## 4. Historical Data Integration

For comprehensive backtesting, the system requires at least 3 years of historical Kraken data. This data will primarily consist of OHLCV (Open, High, Low, Close, Volume) data for the relevant trading pairs and timeframes (1-minute, 5-minute, 4-hour, Daily).

-   **Data Source**: Kraken\`s historical data can be obtained via their REST API or through third-party data providers. For this phase, we will focus on integrating data that can be downloaded or accessed programmatically.
-   **Data Format**: The historical data will be stored in a structured format, such as CSV or Parquet files, with columns for timestamp, open, high, low, close, and volume.
-   **Data Preparation**: The Historical Data Loader will be responsible for:
    -   Loading data from files.
    -   Handling missing data points (e.g., interpolation or forward-fill).
    -   Ensuring data integrity and chronological order.
    -   Resampling or aggregating data to different timeframes as required by the strategies (e.g., converting 1-minute data to 5-minute or 4-hour candles).

## 5. Backtesting Execution

### Simulation Process:

1.  **Initialization**: The backtesting engine will be initialized with the historical data, the chosen strategy (Trend-Following or Scalping), and its specific parameters.
2.  **Event Loop**: The engine will iterate through the historical data, processing each data point as a discrete event.
3.  **Data Feed**: For each historical data point (e.g., a new OHLCV candle), it will be fed to the `StrategyWrapper`.
4.  **Signal Generation**: The `StrategyWrapper` will invoke the strategy\`s `on_ohlcv_update` (and potentially `on_order_book_update` if order book data is used in backtesting) method, which will generate trading signals based on its logic.
5.  **Trade Simulation**: Generated signals will be passed to the `TradeSimulator`. The simulator will:
    -   Check for sufficient capital.
    -   Simulate order execution (e.g., assuming market orders fill at the next bar\`s open or close, or limit orders fill if price is met).
    -   Update simulated portfolio equity, open positions, and realized P/L.
    -   Apply simulated transaction fees.
6.  **Risk Management Simulation**: The backtesting engine will also simulate the `Risk & Execution Agent`\`s critical risk checks (position sizing, max portfolio drawdown, volatility halt) to see how they would have impacted historical performance.

### Output and Reporting:

Upon completion of the backtest, the Performance Analyzer will generate a detailed report including:

-   **Key Metrics**: Total Return, Annualized Return, Volatility, Sharpe Ratio, Sortino Ratio, Max Drawdown, Calmar Ratio.
-   **Trade List**: A list of all simulated trades, including entry/exit prices, P/L, and duration.
-   **Equity Curve**: A visualization of the portfolio\`s equity over time.
-   **Drawdown Plot**: A visualization of the portfolio\`s drawdown over time.

## 6. Backtesting for Both Operating Modes

Each operating mode (Trend-Following and Scalping) will be backtested independently. This involves:

-   **Trend-Following Mode**: Backtesting on 4-hour and Daily OHLCV data, using the 50/200 EMA crossover and RSI filter. The backtest will focus on long-term trends and pullbacks.
-   **Scalping Mode**: Backtesting on 1-minute and 5-minute OHLCV data, using Bollinger Bands and simulated order flow analysis. The backtest will focus on high-frequency, mean-reversion opportunities.

## 7. Configuration

The backtesting environment will be configurable via a dedicated configuration file (e.g., `config/backtesting.yaml`). Key configurable parameters will include:

-   `historical_data_path`: Path to the directory containing historical data files.
-   `start_date`, `end_date`: Date range for the backtest.
-   `initial_capital`: Starting capital for the simulation.
-   `strategy_mode`: (`trend_following` or `scalping`)
-   `strategy_parameters`: Specific parameters for the chosen strategy.
-   `commission_rate`: Simulated trading commission (e.g., 0.1%).
-   `slippage_percent`: Simulated slippage per trade (e.g., 0.01%).

This design provides a robust framework for evaluating the performance of the trading strategies using historical data, which is essential before moving to paper or live trading.




## 9. Paper Trading Environment Design



# Paper Trading Environment: Design for Setup and Execution

## 1. Overview

Paper trading (or simulated trading) is the second critical phase in the development lifecycle of the Kraken Multi-Agent Cryptocurrency Trading System. Following successful backtesting, paper trading allows for the deployment of the system in a real-time, simulated environment using the Kraken API sandbox. This phase bridges the gap between historical data simulation and live deployment, providing a realistic testing ground without risking actual capital.

## 2. Objectives of Paper Trading

The primary objectives of the paper trading phase are to:

-   **Validate Real-time Integration**: Confirm that all agents (Market Data, Strategy & Signal, Risk & Execution, Dashboard) integrate and communicate seamlessly in a live, asynchronous environment.
-   **Test API Interactions**: Verify the correct functioning of the API management framework, including key rotation, rate-limit awareness, and error handling, against Kraken\`s actual API endpoints (sandbox).
-   **Evaluate Strategy Performance in Live Conditions**: Observe how the Trend-Following and Scalping strategies perform with real-time market data and real-world latency, slippage, and order book dynamics.
-   **Monitor Risk Management Protocols**: Ensure that the Master Circuit Breaker, Volatility Halt, and API/Exchange Error Handling mechanisms trigger correctly and perform their intended actions under simulated adverse conditions.
-   **Assess System Stability and Uptime**: Identify any memory leaks, race conditions, or other stability issues that might arise during continuous operation over an extended period (at least one week).
-   **Refine Dashboard Feedback**: Verify that the Dashboard Agent provides accurate, timely, and comprehensive information to the user.

## 3. Paper Trading Environment Setup

The paper trading environment will leverage the existing agent architecture but will be configured to interact with Kraken\`s sandbox API endpoints instead of the live production endpoints. This requires careful configuration and potentially minor adjustments to the `KrakenAPIClient` to point to the sandbox URLs.

### Key Setup Components:

1.  **Kraken API Sandbox Credentials**: The user will need to provide separate API keys and secrets specifically generated for the Kraken sandbox environment. These will be loaded securely, similar to live API keys.
2.  **Configuration Management**: A dedicated configuration file (e.g., `config/paper_trading.yaml` or environment variables) will be used to specify that the system should operate in paper trading mode and use sandbox API endpoints.
3.  **Agent Configuration**: Each agent will be configured to operate in paper trading mode:
    -   **Market Data Agent**: Will connect to Kraken\`s real-time WebSocket feeds for the sandbox environment (if available and distinct from production, otherwise production data will be used for market data, but orders will go to sandbox).
    -   **Strategy & Signal Agent**: Will operate as designed, consuming real-time data and generating signals.
    -   **Risk & Execution Agent**: This is the most critical component for paper trading. Its `KrakenAPIClient` instances *must* be configured to send all orders and queries to the Kraken sandbox API. It will simulate actual order placement and receive simulated fills from the sandbox.
    -   **Dashboard Agent**: Will display real-time information based on the simulated trades and portfolio performance in the sandbox.

## 4. Deployment in Paper Trading Mode

Deploying the system in paper trading mode involves running all agents concurrently, configured for the sandbox environment. This will typically be done on a persistent server or a local machine that can run continuously for at least one week.

### Deployment Steps:

1.  **Environment Preparation**: Ensure all necessary Python dependencies are installed (`pip install -r requirements.txt`).
2.  **Configuration**: Set up the `config/paper_trading.yaml` (or equivalent environment variables) with the sandbox API credentials and `paper_trading_mode: True`.
3.  **Execution**: Start the main application script that initializes and runs all four agents in their respective asynchronous tasks or processes.
    -   The `MarketDataAgent` will start streaming real-time data.
    -   The `Strategy & Signal Agent` will start generating signals.
    -   The `Risk & Execution Agent` will process signals, perform risk checks, and interact with the Kraken sandbox API.
    -   The `Dashboard Agent` will provide continuous real-time feedback.

## 5. Monitoring Paper Trading Performance

Continuous monitoring is essential during the paper trading phase. The Dashboard Agent will be the primary tool for real-time observation, but additional logging and data persistence will be crucial for post-analysis.

### Monitoring Aspects:

-   **Dashboard Agent**: Regularly observe the CLI dashboard for:
    -   `TOTAL EQUITY`, `REALIZED P/L`, `UNREALIZED P/L`: To track simulated portfolio growth or decline.
    -   `MAX DRAWDOWN LIMIT`, `CURRENT DRAWDOWN`: To ensure risk controls are effective.
    -   `API STATUS`: To verify API key rotation, quarantine, and rate-limit management are working as expected.
    -   `OPEN POSITIONS`: To confirm correct position management.
    -   `SYSTEM LOG`: To identify any errors, warnings, or unexpected behaviors.
-   **Detailed Logging**: All agent activities, API interactions, signal generations, order placements, and risk management triggers will be logged to files. These logs will be invaluable for debugging and post-mortem analysis.
-   **Simulated Trade History**: The `Risk & Execution Agent` should maintain a detailed record of all simulated trades (entry, exit, P/L) in a persistent storage (e.g., a CSV file or a simple database) for later analysis.
-   **Performance Metrics**: At the end of the paper trading period, a summary report similar to the backtesting report will be generated, detailing the simulated performance over the week.

### Duration:

The system must be deployed in a paper trading environment for at least one week. This duration allows for sufficient exposure to various market conditions and helps uncover long-running stability issues.

## 6. Transition to Live Deployment

Live deployment will only proceed after successful completion of the paper trading phase. \"Successful completion\" implies:

-   No critical errors or unexpected halts during the entire paper trading period.
-   Risk management protocols functioning as intended.
-   API management framework demonstrating resilience and efficiency.
-   Strategy performance aligning with expectations (even if not profitable, understanding why is key).
-   All dashboard metrics and logs providing accurate and reliable information.

This design ensures a thorough and cautious approach to testing the trading system in a near-live environment before committing real capital.




## 10. Live Deployment Preparation



# Live Deployment: Preparation and User Confirmation

## 1. Overview

Live deployment is the final and most critical phase of the Kraken Multi-Agent Cryptocurrency Trading System. After successful backtesting and paper trading, this phase involves deploying the system to interact with real Kraken exchange accounts and live market data, risking actual capital. Due to the inherent risks, this phase requires meticulous preparation and explicit user confirmation before activation.

## 2. Objectives of Live Deployment

The primary objectives of the live deployment phase are to:

-   **Execute Real Trades**: Interact with the live Kraken API to place, manage, and cancel orders using real capital.
-   **Generate Real P/L**: Aim to generate actual profit or loss based on the performance of the trading strategies.
-   **Operate Continuously**: Maintain maximum uptime and efficiency in a production environment.
-   **Ensure Robustness**: Confirm the system\`s resilience to real-world market conditions, network fluctuations, and API eccentricities.
-   **Provide Real-time Visibility**: Offer accurate and timely insights into live trading operations through the Dashboard Agent.

## 3. Preparation for Live Deployment

Transitioning from paper trading to live deployment requires careful configuration and a final review of all system components.

### 3.1. API Key Management

-   **Production API Keys**: The system will require valid Kraken API keys and secrets that have permissions for live trading (e.g., `Trade` permission). These keys must be distinct from the sandbox keys used during paper trading.
-   **Secure Storage**: Ensure that production API keys are stored and loaded securely. Environment variables (as implemented in `main.py` using `python-dotenv`) are a suitable method, preventing keys from being hardcoded or committed to version control.
-   **Permissions Review**: Double-check that the API keys have only the necessary permissions (e.g., `Query Funds`, `Query Open Orders & Trades`, `Place & Manage Orders`). Avoid granting unnecessary permissions.

### 3.2. Configuration Adjustments

-   **`USE_SANDBOX` Flag**: The `main.py` script includes a `USE_SANDBOX` environment variable. For live deployment, this flag *must* be set to `False` (or equivalent, depending on implementation) to ensure the system connects to Kraken\`s production API endpoints.
-   **Initial Portfolio Equity**: The `initial_portfolio_equity` value in the `RiskExecutionAgent` should accurately reflect the actual capital allocated for live trading. This is crucial for correct position sizing and drawdown calculations.
-   **Risk Parameters**: Reconfirm the `MAX_PORTFOLIO_DRAWDOWN` and strategy-specific `risk_percentage` values. These parameters directly control the capital at risk and should be set conservatively for initial live deployment.
-   **Trading Pairs**: Verify that the `TRADING_PAIRS` configured are the desired assets for live trading.

### 3.3. System Review and Checklist

Before initiating live trading, a final checklist should be reviewed:

-   [ ] **Backtesting Results**: Have the backtesting results been thoroughly analyzed and deemed satisfactory? Are the strategies profitable and within acceptable risk parameters on historical data?
-   [ ] **Paper Trading Performance**: Has the system performed stably and as expected during the paper trading phase for at least one week? Were there any critical errors or unexpected behaviors that need addressing?
-   [ ] **API Connectivity**: Can the system successfully connect to Kraken\`s live API endpoints using the production API keys?
-   [ ] **Risk Controls**: Are the Master Circuit Breaker and Volatility Halt mechanisms correctly configured and tested (conceptually, as they are hard to test in paper trading)?
-   [ ] **Monitoring**: Is the Dashboard Agent fully functional and providing all necessary real-time information?
-   [ ] **Logging**: Is comprehensive logging enabled and configured to capture all critical events for post-mortem analysis and debugging?
-   [ ] **Emergency Shutdown Plan**: Is there a clear and tested procedure for manually stopping the system and closing all positions in an emergency?
-   [ ] **Capital Allocation**: Is the allocated capital for live trading appropriate and within acceptable personal risk tolerance?

## 4. User Confirmation for Live Deployment

Given the financial implications of live trading, explicit user confirmation is absolutely mandatory before the system is activated in a live environment. The system will prompt the user for this confirmation, outlining the risks involved.

### Confirmation Process:

1.  **Prompt**: The system will present a clear message to the user, stating that it is ready for live deployment and reiterating the risks of trading with real capital.
2.  **User Input**: The user will be required to provide a specific confirmation (e.g., typing `YES_I_UNDERSTAND`) to proceed. Any other input will abort the live deployment.
3.  **Final Check**: Upon confirmation, the system will perform a final check of critical configurations (e.g., `USE_SANDBOX` is `False`).
4.  **Activation**: Only after successful confirmation and final checks will the system proceed to connect to the live Kraken API and begin trading.

This structured approach ensures that the transition to live trading is made with full awareness of the risks and with all necessary safeguards in place.


