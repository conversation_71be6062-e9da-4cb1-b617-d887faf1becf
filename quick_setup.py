#!/usr/bin/env python3
"""
Quick Setup for Kraken Multi-Agent Trading System
Automatically validates and displays configuration from .env file
"""

import os
from pathlib import Path

def print_header():
    """Print setup header"""
    print("=" * 80)
    print("         KRAKEN MULTI-AGENT LIVE TRADING SYSTEM")
    print("                    QUICK SETUP VALIDATOR")
    print("                    ⚠️  REAL MONEY AT RISK ⚠️")
    print("=" * 80)
    print()

def load_and_display_config():
    """Load and display current configuration"""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        print("📋 CURRENT CONFIGURATION:")
        print("-" * 50)
        
        # API Keys
        api_keys_found = 0
        for i in range(1, 6):
            key = os.getenv(f"KRAKEN_API_KEY_{i}", "")
            secret = os.getenv(f"KRAKEN_API_SECRET_{i}", "")
            
            if key and secret:
                api_keys_found += 1
                print(f"✓ API Key {i}: {key[:8]}...{key[-4:]} (configured)")
            else:
                print(f"❌ API Key {i}: Not configured")
        
        print(f"\nAPI Keys Status: {api_keys_found}/5 configured")
        
        # Trading Configuration
        print("\n📊 TRADING SETTINGS:")
        print("-" * 30)
        operating_mode = os.getenv("OPERATING_MODE", "trend_following")
        trading_pairs = os.getenv("TRADING_PAIRS", "XBT/USD,ETH/USD")
        initial_portfolio = os.getenv("INITIAL_PORTFOLIO_VALUE", "10000.0")
        
        print(f"Operating Mode:     {operating_mode}")
        print(f"Trading Pairs:      {trading_pairs}")
        print(f"Initial Portfolio:  ${float(initial_portfolio):,.2f}")
        
        # Risk Management
        print("\n⚖️ RISK MANAGEMENT:")
        print("-" * 30)
        max_drawdown = float(os.getenv("MAX_PORTFOLIO_DRAWDOWN", "0.50"))
        max_position = float(os.getenv("MAX_POSITION_SIZE", "0.10"))
        stop_loss = float(os.getenv("STOP_LOSS_PERCENTAGE", "0.02"))
        take_profit = float(os.getenv("TAKE_PROFIT_PERCENTAGE", "0.04"))
        
        print(f"Max Drawdown:       {max_drawdown:.1%}")
        print(f"Max Position Size:  {max_position:.1%}")
        print(f"Stop Loss:          {stop_loss:.1%}")
        print(f"Take Profit:        {take_profit:.1%}")
        
        # System Settings
        print("\n🔧 SYSTEM SETTINGS:")
        print("-" * 30)
        log_level = os.getenv("LOG_LEVEL", "INFO")
        api_rate_limit = os.getenv("API_RATE_LIMIT", "20")
        dashboard_refresh = os.getenv("DASHBOARD_REFRESH_RATE", "1.0")
        
        print(f"Log Level:          {log_level}")
        print(f"API Rate Limit:     {api_rate_limit} req/min")
        print(f"Dashboard Refresh:  {dashboard_refresh}s")
        
        return api_keys_found >= 5
        
    except ImportError:
        print("❌ python-dotenv not installed. Run: pip install python-dotenv")
        return False
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")
        return False

def validate_system():
    """Validate the system is ready"""
    try:
        from src.utils.config import load_config
        config = load_config()
        
        print("\n✅ SYSTEM VALIDATION:")
        print("-" * 30)
        print(f"✓ Configuration loaded successfully")
        print(f"✓ {len(config.kraken_api_keys)} API keys ready")
        print(f"✓ {len(config.trading_pairs)} trading pairs configured")
        print(f"✓ Risk management rules active")
        
        return True
        
    except Exception as e:
        print(f"\n❌ SYSTEM VALIDATION FAILED:")
        print(f"Error: {e}")
        return False

def main():
    """Main setup function"""
    print_header()
    
    # Check if .env exists
    if not Path(".env").exists():
        print("❌ No .env file found!")
        print("\nTo get started:")
        print("1. Run: python trading_configurator.py")
        print("2. Or manually create .env file with your API keys")
        return
    
    print("📁 Found .env file - loading configuration...")
    print()
    
    # Load and display configuration
    config_ok = load_and_display_config()
    
    if not config_ok:
        print("\n❌ CONFIGURATION INCOMPLETE")
        print("\nTo fix this:")
        print("1. Run: python trading_configurator.py")
        print("2. Make sure all 5 API keys are configured")
        return
    
    # Validate system
    system_ok = validate_system()
    
    if system_ok:
        print("\n" + "=" * 80)
        print("                    🎉 SYSTEM READY FOR LIVE TRADING! 🎉")
        print("=" * 80)
        print()
        print("Your system is properly configured and ready to trade.")
        print("⚠️  WARNING: This system will trade with REAL MONEY!")
        print()
        print("Next steps:")
        print("1. Run system tests: python test_system.py")
        print("2. Start live trading: python main.py")
        print("3. Or use the launcher: launch_trading_system.bat")
        print()
        print("🔴 REMEMBER: Always monitor your trades and understand the risks!")
        print("=" * 80)
    else:
        print("\n❌ SYSTEM NOT READY")
        print("\nPlease fix the configuration errors above.")
        print("Run: python trading_configurator.py")

if __name__ == "__main__":
    main()
