import asyncio
import logging
import os
import time
from collections import deque

# Configure logging to capture messages from all agents
# This handler will direct logs to a queue that the DashboardAgent can read
class QueueHandler(logging.Handler):
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.put_nowait(self.format(record))

log_queue = asyncio.Queue() # Global queue for logs

# Remove default handlers and add our custom handler
for handler in logging.root.handlers[:]:
    logging.root.removeHandler(handler)
logging.basicConfig(level=logging.INFO, format=\'%(asctime)s - %(levelname)s - %(message)s\')
logging.getLogger().addHandler(QueueHandler(log_queue))


class DashboardAgent:
    def __init__(self, dashboard_queue: asyncio.Queue, log_queue: asyncio.Queue):
        self.dashboard_queue = dashboard_queue
        self.log_queue = log_queue
        self.operating_mode = "N/A"
        self.total_equity = 0.0
        self.realized_pnl_24h = 0.0
        self.unrealized_pnl = 0.0
        self.max_drawdown_limit = 0.0
        self.current_drawdown = 0.0
        self.api_status = {}
        self.open_positions = {}
        self.system_logs = deque(maxlen=20) # Keep last 20 log entries
        logging.info("DashboardAgent initialized.")

    async def _update_display(self):
        os.system("clear") # Clear the terminal screen

        print("=" * 80)
        print("KRAKEN MULTI-AGENT TRADING SYSTEM - REAL-TIME DASHBOARD".center(80))
        print("=" * 80)
        print(f"\nOPERATING MODE: {self.operating_mode:<20} | PAIR: XBT/USD (Example)")
        print("\n" + "-" * 80)
        print("PORTFOLIO SUMMARY".center(80))
        print("-" * 80)
        print(f"TOTAL EQUITY:             {self.total_equity:>15.2f} USD")
        print(f"REALIZED P/L (24h):       {self.realized_pnl_24h:>15.2f} USD")
        print(f"UNREALIZED P/L:           {self.unrealized_pnl:>15.2f} USD")
        print(f"MAX DRAWDOWN LIMIT:       {self.max_drawdown_limit:>15.2f}%")
        print(f"CURRENT DRAWDOWN:         {self.current_drawdown:>15.2f}%")

        print("\n" + "-" * 80)
        print("API STATUS".center(80))
        print("-" * 80)
        if not self.api_status:
            print("No API keys configured or status available.")
        else:
            for key_id, status_info in self.api_status.items():
                status_str = status_info["status"]
                extra_info = ""
                if status_str == "QUARANTINED":
                    extra_info = f" (Until: {time.ctime(status_info[\"quarantine_until\"]).split()[-2]})"
                elif status_str == "DISABLED":
                    extra_info = f" (Reason: {status_info[\"reason\"]})"
                print(f"Key {key_id}: {status_str:<10}{extra_info}")

        print("\n" + "-" * 80)
        print("OPEN POSITIONS".center(80))
        print("-" * 80)
        if not self.open_positions:
            print("No open positions.")
        else:
            print(f"{\"PAIR\":<10} | {\"TYPE\":<5} | {\"VOLUME\":<10} | {\"ENTRY PRICE\":<12} | {\"CURRENT PRICE\":<14} | {\"UNREALIZED P/L\":<15}")
            print("-" * 80)
            for pair, pos_info in self.open_positions.items():
                # Dummy current price and P/L calculation for display
                current_price = pos_info["avg_entry_price"] * 1.001 # Simulate slight gain
                unrealized_pnl_usd = (current_price - pos_info["avg_entry_price"]) * pos_info["volume"]
                unrealized_pnl_percent = (unrealized_pnl_usd / (pos_info["avg_entry_price"] * pos_info["volume"])) * 100 if (pos_info["avg_entry_price"] * pos_info["volume"]) else 0
                print(f"{pair:<10} | {\"LONG\":<5} | {pos_info[\"volume\"]:<10.5f} | {pos_info[\"avg_entry_price\"]:<12.2f} | {current_price:<14.2f} | {unrealized_pnl_usd:<+10.2f} ({unrealized_pnl_percent:<+6.2f}%) ")

        print("\n" + "-" * 80)
        print("SYSTEM LOG".center(80))
        print("-" * 80)
        if not self.system_logs:
            print("No log entries yet.")
        else:
            for log_entry in self.system_logs:
                print(log_entry)
        print("=" * 80)

    async def _process_dashboard_updates(self):
        while True:
            update = await self.dashboard_queue.get()
            update_type = update.get("type")

            if update_type == "equity_update":
                self.total_equity = update["value"]
                # For simplicity, calculate drawdown based on initial equity
                if self.max_drawdown_limit > 0 and self.total_equity < self.initial_portfolio_equity:
                    self.current_drawdown = ((self.initial_portfolio_equity - self.total_equity) / self.initial_portfolio_equity) * 100
                else:
                    self.current_drawdown = 0.0
            elif update_type == "system_alert":
                self.system_logs.append(f"[ALERT] {update[\"message\"]}")
            elif update_type == "api_status_update":
                key_id = update["key"]
                self.api_status[key_id] = {"status": update["status"], "quarantine_until": update.get("quarantine_until", 0), "reason": update.get("reason", "N/A")}
            elif update_type == "position_update":
                self.open_positions[update["pair"]] = update["details"]
            elif update_type == "operating_mode":
                self.operating_mode = update["mode"]
            elif update_type == "initial_equity":
                self.initial_portfolio_equity = update["value"]
                self.total_equity = update["value"]
                self.max_drawdown_limit = update.get("max_drawdown_limit", 0.0) # Assuming REA sends this
            elif update_type == "order_update":
                self.system_logs.append(f"[ORDER] {update[\"status\"]}: {update[\"order_id\"]} - {update[\"details\"]}")
            else:
                self.system_logs.append(f"[UNKNOWN UPDATE] {update}")

            await self._update_display()

    async def _process_log_queue(self):
        while True:
            log_entry = await self.log_queue.get()
            self.system_logs.append(log_entry)
            await self._update_display()

    async def run(self):
        # Start processing updates from other agents
        asyncio.create_task(self._process_dashboard_updates())
        # Start processing logs from the global log queue
        asyncio.create_task(self._process_log_queue())

        # Initial display
        await self._update_display()

        # Keep the dashboard running
        while True:
            await asyncio.sleep(1) # Refresh rate

async def main():
    # Dummy queues for testing
    dashboard_q = asyncio.Queue()
    global log_queue # Use the global log queue

    # Simulate initial data from REA
    await dashboard_q.put({"type": "initial_equity", "value": 10000.0, "max_drawdown_limit": 50.0})
    await dashboard_q.put({"type": "operating_mode", "mode": "Trend-Following Mode (Long-Term Growth)"})
    await dashboard_q.put({"type": "api_status_update", "key": "key1", "status": "ACTIVE"})
    await dashboard_q.put({"type": "api_status_update", "key": "key2", "status": "QUARANTINED", "quarantine_until": time.time() + 60})

    # Simulate some log messages
    logging.info("MarketDataAgent: Connected to WebSocket.")
    logging.warning("RiskExecutionAgent: VOLATILITY HALT for XBT/USD! Cooling down for 15 minutes.")
    logging.critical("RiskExecutionAgent: MASTER CIRCUIT BREAKER TRIGGERED! Trading Halted.")
    logging.info("StrategySignalAgent: Generated BUY signal for XBT/USD.")

    # Simulate a position update
    await dashboard_q.put({"type": "position_update", "pair": "XBT/USD", "details": {"volume": 0.05, "avg_entry_price": 20000.0}})

    dashboard_agent = DashboardAgent(dashboard_q, log_queue)
    await dashboard_agent.run()

if __name__ == "__main__":
    asyncio.run(main())


