# System Architecture Overview

The Kraken Multi-Agent Cryptocurrency Trading System is designed with a modular architecture, promoting scalability, maintainability, and clear separation of concerns. The system comprises four main agents, each responsible for a specific set of functionalities, communicating through well-defined interfaces.

## High-Level Diagram

```mermaid
graph TD
    User --> DashboardAgent
    KrakenAPI -- WebSocket/REST --> MarketDataAgent
    MarketDataAgent --> StrategySignalAgent
    StrategySignalAgent --> RiskExecutionAgent
    RiskExecutionAgent -- Orders/API Calls --> KrakenAPI
    RiskExecutionAgent -- Status/Logs --> DashboardAgent
```

## Agent Responsibilities:

### 1. Market Data Agent
- **Role**: Data acquisition and distribution.
- **Inputs**: Real-time Level 2 order book and OHLCV data from Kraken WebSockets.
- **Outputs**: Cleaned, standardized market data streams to other agents.
- **Key Considerations**: Prioritizes WebSocket for low-latency, efficient data handling, and robust error recovery for data feeds.

### 2. Strategy & Signal Agent
- **Role**: Algorithmic strategy execution and signal generation.
- **Inputs**: Market data from Market Data Agent.
- **Outputs**: Non-discretionary trading signals (entry, take-profit, stop-loss levels) to Risk & Execution Agent.
- **Key Considerations**: Supports user-selectable operating modes (Trend-Following, Scalping), each with distinct strategies and risk parameters. Focuses on precise signal generation based on technical indicators and market conditions.

### 3. Risk & Execution Agent
- **Role**: Centralized control for trade execution, risk management, and API interaction.
- **Inputs**: Trading signals from Strategy & Signal Agent.
- **Outputs**: Order placement/cancellation requests to Kraken API, status updates/logs to Dashboard Agent.
- **Key Considerations**: Implements critical pre-trade checks (position sizing, portfolio drawdown limits), intelligent API key management (rotation, quarantine), and rate-limit awareness. Acts as the sole interface with the Kraken exchange for order management.

### 4. Dashboard Agent
- **Role**: Real-time system monitoring and user interface.
- **Inputs**: System status, trade logs, and agent-specific information from other agents.
- **Outputs**: Continuously updated command-line interface (CLI) display for the user.
- **Key Considerations**: Operates in a separate thread to ensure non-interruption of trading operations. Provides clear visibility into system health, trading performance, and API status.

## Communication Flow:

- **Market Data Flow**: Unidirectional from Market Data Agent to Strategy & Signal Agent.
- **Signal Flow**: Unidirectional from Strategy & Signal Agent to Risk & Execution Agent.
- **Execution & Status Flow**: Bidirectional between Risk & Execution Agent and Kraken API; unidirectional from Risk & Execution Agent to Dashboard Agent.
- **User Interaction**: Primarily through the Dashboard Agent for monitoring and initial mode selection.

## Data Storage:

- **Ephemeral Data**: Real-time market data and intermediate signals are processed in-memory for speed.
- **Persistent Data**: Trade history, configuration settings, and critical logs will be stored persistently (e.g., in flat files or a lightweight database) for post-analysis and system recovery.

## Error Handling & Resilience:

- Each agent will implement robust error handling for its specific domain.
- Centralized error logging and alerting mechanisms.
- Critical risk management protocols (Master Circuit Breaker, Volatility Halt, API/Exchange Error Handling) are primarily managed by the Risk & Execution Agent to ensure system stability and capital protection.


