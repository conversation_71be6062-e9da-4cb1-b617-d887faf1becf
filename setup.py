#!/usr/bin/env python3
"""
Setup script for Kraken Multi-Agent Cryptocurrency Trading System
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="kraken-multi-agent-trading",
    version="1.0.0",
    author="Kraken Trading System",
    author_email="<EMAIL>",
    description="A modular, multi-agent cryptocurrency trading system for Kraken exchange",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/kraken-multi-agent-trading",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "monitoring": [
            "colorlog>=6.7.0",
            "psutil>=5.9.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "kraken-trading=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.yml", "*.yaml"],
    },
    zip_safe=False,
    keywords="cryptocurrency trading kraken algorithmic-trading multi-agent",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/kraken-multi-agent-trading/issues",
        "Source": "https://github.com/yourusername/kraken-multi-agent-trading",
        "Documentation": "https://github.com/yourusername/kraken-multi-agent-trading/wiki",
    },
)
