#!/usr/bin/env python3
"""
Live Dashboard Launcher for Kraken Multi-Agent Trading System
Standalone dashboard for monitoring live trading activity
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent))

async def main():
    """Launch the live trading dashboard"""
    
    print("=" * 80)
    print("         KRAKEN MULTI-AGENT LIVE TRADING DASHBOARD")
    print("                    ⚠️  REAL MONEY AT RISK ⚠️")
    print("=" * 80)
    print()
    
    try:
        # Import required modules
        from src.agents.dashboard_agent import DashboardAgent
        from src.utils.logging_config import setup_logging, get_log_queue
        from src.utils.config import load_config
        
        # Setup logging
        logger = setup_logging()
        logger.info("Starting Live Trading Dashboard...")
        
        # Load configuration
        try:
            config = load_config()
            logger.info(f"Configuration loaded - {len(config.kraken_api_keys)} API keys configured")
        except Exception as e:
            print(f"❌ Configuration error: {e}")
            print("Please run the trading configurator first: python trading_configurator.py")
            return
        
        # Create dashboard queue (normally this would be shared with other agents)
        dashboard_queue = asyncio.Queue()
        log_queue = get_log_queue()
        
        # Initialize dashboard agent
        dashboard_agent = DashboardAgent(dashboard_queue, log_queue)
        
        # Send initial dashboard data
        await dashboard_queue.put({
            "type": "initial_equity", 
            "value": config.initial_portfolio_value, 
            "max_drawdown_limit": config.max_portfolio_drawdown
        })
        
        await dashboard_queue.put({
            "type": "operating_mode", 
            "mode": f"{config.operating_mode.title()} Mode - LIVE TRADING"
        })
        
        # Simulate API key status
        for i, (api_key, _) in enumerate(config.get_api_keys()):
            await dashboard_queue.put({
                "type": "api_status_update", 
                "key": f"{api_key[:8]}...", 
                "status": "ACTIVE"
            })
        
        # Add some sample log messages
        logger.info("Live Trading Dashboard started")
        logger.warning("⚠️  LIVE TRADING MODE - Real money at risk")
        logger.info(f"Monitoring {len(config.trading_pairs)} trading pairs: {', '.join(config.trading_pairs)}")
        
        print("🔴 LIVE TRADING DASHBOARD ACTIVE 🔴")
        print("Press Ctrl+C to stop the dashboard")
        print()
        
        # Run the dashboard
        await dashboard_agent.run()
        
    except KeyboardInterrupt:
        print("\n" + "=" * 80)
        print("Dashboard stopped by user. Goodbye! 👋")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please make sure all dependencies are installed:")
        print("  pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
