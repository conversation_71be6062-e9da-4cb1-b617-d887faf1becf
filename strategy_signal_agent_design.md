# Strategy & Signal Agent: Design for Strategy Module Interface and Signal Generation

## 1. Overview

The Strategy & Signal Agent (SSA) is a critical component of the Kraken Multi-Agent Cryptocurrency Trading System, responsible for ingesting processed market data from the Market Data Agent, applying defined trading strategies, and generating precise, non-discretionary trading signals. These signals, which include entry, take-profit, and stop-loss levels, are then passed to the Risk & Execution Agent for order placement. The SSA is designed to be modular, allowing for easy integration of new trading strategies and user-selectable operational modes.

## 2. Responsibilities

The primary responsibilities of the Strategy & Signal Agent include:

-   **Data Ingestion**: Receiving real-time Level 2 order book and OHLCV data streams from the Market Data Agent.
-   **Strategy Execution**: Applying the logic of the currently selected trading strategy (Trend-Following or Scalping).
-   **Signal Generation**: Producing clear, actionable trading signals (entry, take-profit, stop-loss) based on strategy rules and market conditions.
-   **Risk Parameter Integration**: Incorporating strategy-specific risk parameters (e.g., default risk percentage per trade) into signal generation.
-   **Filtering and Confirmation**: Utilizing additional indicators (e.g., RSI for Trend-Following) to confirm signals and reduce false positives.

## 3. Strategy Module Interface Design

To ensure modularity and extensibility, each trading strategy will adhere to a common interface. This interface will define the methods and properties that every strategy module must implement, allowing the SSA to dynamically load and execute different strategies based on user selection.

### `BaseStrategy` Abstract Class

All concrete strategy implementations will inherit from a `BaseStrategy` abstract class, which will define the common interface. This ensures that all strategies provide the necessary methods for the SSA to interact with them.

```python
from abc import ABC, abstractmethod

class BaseStrategy(ABC):
    def __init__(self, pair: str, timeframe: str, risk_percentage: float):
        self.pair = pair
        self.timeframe = timeframe
        self.risk_percentage = risk_percentage
        self.signals = [] # To store generated signals

    @abstractmethod
    async def on_ohlcv_update(self, ohlcv_data: dict):
        """
        Processes new OHLCV data and generates trading signals.
        """
        pass

    @abstractmethod
    async def on_order_book_update(self, order_book_data: dict):
        """
        Processes new order book data (if relevant to the strategy).
        """
        pass

    @abstractmethod
    def get_signals(self) -> list:
        """
        Returns a list of generated trading signals.
        Each signal should be a dictionary containing:
        {
            'type': 'entry' | 'take_profit' | 'stop_loss',
            'pair': str,
            'price': float,
            'timestamp': float,
            'strategy_id': str,
            'risk_percentage': float # Risk for this specific trade
        }
        """
        pass

    @abstractmethod
    def get_required_data_streams(self) -> dict:
        """
        Returns a dictionary specifying the data streams required by the strategy.
        Example: {'ohlc': [('XBT/USD', '1'), ('XBT/USD', '5')], 'book': [('XBT/USD')]}
        """
        pass

    def clear_signals(self):
        """
        Clears the list of generated signals after they have been processed.
        """
        self.signals = []

```

### Concrete Strategy Implementations

Each specific trading strategy (e.g., `TrendFollowingStrategy`, `ScalpingStrategy`) will be a concrete implementation of the `BaseStrategy` class. They will implement the `on_ohlcv_update`, `on_order_book_update`, `get_signals`, and `get_required_data_streams` methods according to their specific logic.

## 4. Signal Generation and Structure

Trading signals generated by the SSA will be non-discretionary and will contain all necessary information for the Risk & Execution Agent to place an order. A signal will be represented as a dictionary with the following structure:

| Field             | Type    | Description                                                                 |
| :---------------- | :------ | :-------------------------------------------------------------------------- |
| `type`            | `str`   | Type of signal: `entry`, `take_profit`, or `stop_loss`.                     |
| `pair`            | `str`   | Trading pair for the signal (e.g., `XBT/USD`).                              |
| `price`           | `float` | The target price for the signal.                                            |
| `timestamp`       | `float` | Unix timestamp when the signal was generated.                               |
| `strategy_id`     | `str`   | Unique identifier for the strategy that generated the signal.               |
| `risk_percentage` | `float` | The percentage of total portfolio equity to risk on this specific trade.    |

## 5. Data Flow within SSA

1.  **Initialization**: The SSA will be initialized with the user-selected `Operating_Mode` and associated configuration (e.g., trading pairs, timeframes, default risk).
2.  **Strategy Instantiation**: Based on the selected mode, the appropriate concrete strategy class will be instantiated.
3.  **Data Consumption**: The SSA will continuously consume data from the Market Data Agent's queues. When new OHLCV or order book data arrives, it will be passed to the `on_ohlcv_update` or `on_order_book_update` method of the active strategy instance.
4.  **Signal Retrieval**: Periodically, or upon specific events (e.g., new candle close), the SSA will call the `get_signals` method of the active strategy. Any generated signals will be retrieved.
5.  **Signal Forwarding**: Retrieved signals will be immediately forwarded to the Risk & Execution Agent's input queue.
6.  **Signal Clearing**: After forwarding, the `clear_signals` method of the strategy will be called to prevent duplicate processing.

## 6. User-Selectable Operating Modes

The SSA will support two primary operating modes, each corresponding to a distinct trading strategy:

### [A] Trend-Following Mode (Long-Term Growth)

-   **Timeframe**: Primarily 4-hour and Daily charts.
-   **Strategy**: Identifies established trends using a 50/200 Exponential Moving Average (EMA) crossover system. An entry signal is generated only in the direction of the trend on a confirmed pullback, with the Relative Strength Index (RSI) used as a confirmation filter.
-   **Default Risk**: No single trade shall risk more than 2% of total portfolio equity.

### [B] Scalping Mode (Short-Term Gains)

-   **Timeframe**: Primarily 1-minute and 5-minute charts.
-   **Strategy**: A high-frequency, mean-reversion strategy using Bollinger Bands and order flow analysis. Entry signals are generated on sharp deviations from the mean, expecting a quick reversion.
-   **Default Risk**: No single trade shall risk more than 0.5% of total portfolio equity.

## 7. Configuration

The SSA will be configurable, allowing for dynamic selection of operating modes and adjustment of strategy parameters. This will likely involve a configuration file (e.g., `config/strategy_agent.yaml`) or environment variables.

Key configurable parameters will include:

-   `operating_mode`: (`trend_following` or `scalping`)
-   `trading_pairs`: List of pairs the SSA should monitor.
-   `trend_following_params`:
    -   `ema_short_period`: 50
    -   `ema_long_period`: 200
    -   `rsi_period`: 14
    -   `rsi_overbought`: 70
    -   `rsi_oversold`: 30
    -   `default_risk_percentage`: 0.02 (2%)
-   `scalping_params`:
    -   `bollinger_period`: 20
    -   `bollinger_std_dev`: 2
    -   `default_risk_percentage`: 0.005 (0.5%)

This design provides a clear roadmap for implementing a flexible and robust Strategy & Signal Agent capable of supporting multiple trading strategies and adapting to user requirements.

