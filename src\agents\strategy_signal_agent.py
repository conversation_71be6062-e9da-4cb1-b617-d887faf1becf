import asyncio
import logging
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from .market_data_agent import MarketDataAgent

logger = logging.getLogger(__name__)

class BaseStrategy(ABC):
    """
    Abstract base class for all trading strategies.
    
    Each strategy must implement methods for processing market data
    and generating trading signals.
    """
    
    def __init__(self, pair: str, timeframe: str, risk_percentage: float):
        self.pair = pair
        self.timeframe = timeframe
        self.risk_percentage = risk_percentage
        self.signals: List[Dict] = []
        self.is_active = True

    @abstractmethod
    async def on_ohlcv_update(self, ohlcv_data: dict):
        """Process OHLCV data update"""
        pass

    @abstractmethod
    async def on_order_book_update(self, order_book_data: dict):
        """Process order book data update"""
        pass

    @abstractmethod
    def get_signals(self) -> List[Dict]:
        """Get current trading signals"""
        pass

    @abstractmethod
    def get_required_data_streams(self) -> Dict[str, List]:
        """Get required data streams for this strategy"""
        pass

    def clear_signals(self):
        """Clear all current signals"""
        self.signals = []

    def activate(self):
        """Activate the strategy"""
        self.is_active = True
        logger.info(f"Strategy {self.__class__.__name__} for {self.pair} activated")

    def deactivate(self):
        """Deactivate the strategy"""
        self.is_active = False
        logger.info(f"Strategy {self.__class__.__name__} for {self.pair} deactivated")

class TrendFollowingStrategy(BaseStrategy):
    """
    Trend-following strategy using EMA crossover and RSI confirmation.
    
    Entry conditions:
    - EMA short > EMA long (uptrend) AND RSI < oversold (for buy)
    - EMA short < EMA long (downtrend) AND RSI > overbought (for sell)
    """
    
    def __init__(self, pair: str, timeframe: str, risk_percentage: float, 
                 ema_short_period: int = 50, ema_long_period: int = 200, 
                 rsi_period: int = 14, rsi_overbought: int = 70, rsi_oversold: int = 30):
        super().__init__(pair, timeframe, risk_percentage)
        self.ema_short_period = ema_short_period
        self.ema_long_period = ema_long_period
        self.rsi_period = rsi_period
        self.rsi_overbought = rsi_overbought
        self.rsi_oversold = rsi_oversold
        self.prices = pd.Series(dtype=float)
        self.min_data_points = max(self.ema_long_period, self.rsi_period) + 10

    async def on_ohlcv_update(self, ohlcv_data: dict):
        """Process OHLCV data and generate signals"""
        if not self.is_active:
            return
            
        try:
            close_price = float(ohlcv_data[4])
            timestamp = float(ohlcv_data[0])
            
            # Add new price data
            self.prices = pd.concat([self.prices, pd.Series([close_price], index=[timestamp])])
            
            # Keep only necessary data points for efficiency
            max_keep = self.ema_long_period * 3
            if len(self.prices) > max_keep:
                self.prices = self.prices.iloc[-max_keep:]

            # Check if we have enough data
            if len(self.prices) < self.min_data_points:
                logger.debug(f"Not enough data for {self.pair} trend analysis. "
                           f"Current: {len(self.prices)}, Required: {self.min_data_points}")
                return

            # Calculate indicators
            ema_short = self.prices.ewm(span=self.ema_short_period, adjust=False).mean().iloc[-1]
            ema_long = self.prices.ewm(span=self.ema_long_period, adjust=False).mean().iloc[-1]

            # Calculate RSI
            delta = self.prices.diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.ewm(span=self.rsi_period, adjust=False).mean()
            avg_loss = loss.ewm(span=self.rsi_period, adjust=False).mean()
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs)).iloc[-1]

            logger.debug(f"[{self.pair}-{self.timeframe}] EMA_Short: {ema_short:.2f}, "
                        f"EMA_Long: {ema_long:.2f}, RSI: {rsi:.2f}")

            # Generate signals
            if ema_short > ema_long and rsi < self.rsi_oversold:
                signal = {
                    'type': 'entry',
                    'direction': 'buy',
                    'pair': self.pair,
                    'price': close_price,
                    'timestamp': timestamp,
                    'strategy_id': 'trend_following',
                    'risk_percentage': self.risk_percentage,
                    'indicators': {
                        'ema_short': ema_short,
                        'ema_long': ema_long,
                        'rsi': rsi
                    }
                }
                self.signals.append(signal)
                logger.info(f"Generated BUY signal for {self.pair}: Price={close_price:.2f}, "
                          f"RSI={rsi:.2f}")
                
            elif ema_short < ema_long and rsi > self.rsi_overbought:
                signal = {
                    'type': 'entry',
                    'direction': 'sell',
                    'pair': self.pair,
                    'price': close_price,
                    'timestamp': timestamp,
                    'strategy_id': 'trend_following',
                    'risk_percentage': self.risk_percentage,
                    'indicators': {
                        'ema_short': ema_short,
                        'ema_long': ema_long,
                        'rsi': rsi
                    }
                }
                # Note: Uncomment to enable sell signals
                # self.signals.append(signal)
                logger.info(f"Generated SELL signal for {self.pair}: Price={close_price:.2f}, "
                          f"RSI={rsi:.2f}")

        except Exception as e:
            logger.error(f"Error processing OHLCV data in TrendFollowingStrategy for {self.pair}: {e}")

    async def on_order_book_update(self, order_book_data: dict):
        """Process order book updates (not used in trend following)"""
        pass

    def get_signals(self) -> List[Dict]:
        """Get current signals"""
        return self.signals

    def get_required_data_streams(self) -> Dict[str, List]:
        """Get required data streams"""
        return {
            'ohlc': [(self.pair, self.timeframe)]
        }

class ScalpingStrategy(BaseStrategy):
    """
    Scalping strategy using Bollinger Bands and volume analysis.
    
    Entry conditions:
    - Price touches lower Bollinger Band + high volume (for buy)
    - Price touches upper Bollinger Band + high volume (for sell)
    """
    
    def __init__(self, pair: str, timeframe: str, risk_percentage: float,
                 bollinger_period: int = 20, bollinger_std_dev: float = 2.0,
                 volume_threshold_multiplier: float = 1.5):
        super().__init__(pair, timeframe, risk_percentage)
        self.bollinger_period = bollinger_period
        self.bollinger_std_dev = bollinger_std_dev
        self.volume_threshold_multiplier = volume_threshold_multiplier
        self.prices = pd.Series(dtype=float)
        self.volumes = pd.Series(dtype=float)
        self.min_data_points = self.bollinger_period + 10

    async def on_ohlcv_update(self, ohlcv_data: dict):
        """Process OHLCV data and generate signals"""
        if not self.is_active:
            return
            
        try:
            close_price = float(ohlcv_data[4])
            volume = float(ohlcv_data[6])
            timestamp = float(ohlcv_data[0])

            # Add new data
            self.prices = pd.concat([self.prices, pd.Series([close_price], index=[timestamp])])
            self.volumes = pd.concat([self.volumes, pd.Series([volume], index=[timestamp])])

            # Keep only necessary data points
            max_keep = self.bollinger_period * 3
            if len(self.prices) > max_keep:
                self.prices = self.prices.iloc[-max_keep:]
                self.volumes = self.volumes.iloc[-max_keep:]

            # Check if we have enough data
            if len(self.prices) < self.min_data_points:
                logger.debug(f"Not enough data for {self.pair} scalping analysis. "
                           f"Current: {len(self.prices)}, Required: {self.min_data_points}")
                return

            # Calculate Bollinger Bands
            rolling_mean = self.prices.rolling(window=self.bollinger_period).mean()
            rolling_std = self.prices.rolling(window=self.bollinger_period).std()
            upper_band = rolling_mean + (rolling_std * self.bollinger_std_dev)
            lower_band = rolling_mean - (rolling_std * self.bollinger_std_dev)

            current_mean = rolling_mean.iloc[-1]
            current_upper = upper_band.iloc[-1]
            current_lower = lower_band.iloc[-1]

            # Volume analysis
            avg_volume = self.volumes.mean()
            is_high_volume = volume > (avg_volume * self.volume_threshold_multiplier)

            logger.debug(f"[{self.pair}-{self.timeframe}] Close: {close_price:.2f}, "
                        f"Mean: {current_mean:.2f}, Upper: {current_upper:.2f}, "
                        f"Lower: {current_lower:.2f}, Volume: {volume:.2f}, High Volume: {is_high_volume}")

            # Generate signals
            if close_price <= current_lower and is_high_volume:
                signal = {
                    'type': 'entry',
                    'direction': 'buy',
                    'pair': self.pair,
                    'price': close_price,
                    'timestamp': timestamp,
                    'strategy_id': 'scalping',
                    'risk_percentage': self.risk_percentage,
                    'indicators': {
                        'bollinger_upper': current_upper,
                        'bollinger_lower': current_lower,
                        'bollinger_mean': current_mean,
                        'volume': volume,
                        'avg_volume': avg_volume
                    }
                }
                self.signals.append(signal)
                logger.info(f"Generated BUY signal (Scalping) for {self.pair}: "
                          f"Price={close_price:.2f}, Lower Band={current_lower:.2f}")
                
            elif close_price >= current_upper and is_high_volume:
                signal = {
                    'type': 'entry',
                    'direction': 'sell',
                    'pair': self.pair,
                    'price': close_price,
                    'timestamp': timestamp,
                    'strategy_id': 'scalping',
                    'risk_percentage': self.risk_percentage,
                    'indicators': {
                        'bollinger_upper': current_upper,
                        'bollinger_lower': current_lower,
                        'bollinger_mean': current_mean,
                        'volume': volume,
                        'avg_volume': avg_volume
                    }
                }
                # Note: Uncomment to enable sell signals
                # self.signals.append(signal)
                logger.info(f"Generated SELL signal (Scalping) for {self.pair}: "
                          f"Price={close_price:.2f}, Upper Band={current_upper:.2f}")

        except Exception as e:
            logger.error(f"Error processing OHLCV data in ScalpingStrategy for {self.pair}: {e}")

    async def on_order_book_update(self, order_book_data: dict):
        """Process order book updates (could be used for spread analysis)"""
        pass

    def get_signals(self) -> List[Dict]:
        """Get current signals"""
        return self.signals

    def get_required_data_streams(self) -> Dict[str, List]:
        """Get required data streams"""
        return {
            'ohlc': [(self.pair, self.timeframe)]
        }

class StrategySignalAgent:
    """
    Strategy & Signal Agent for generating trading signals.

    Responsibilities:
    - Initialize and manage trading strategies based on operating mode
    - Subscribe to required market data streams
    - Process market data through strategies
    - Forward generated signals to Risk & Execution Agent
    """

    def __init__(self, market_data_agent: MarketDataAgent, operating_mode: str, trading_pairs: List[str]):
        self.market_data_agent = market_data_agent
        self.operating_mode = operating_mode
        self.trading_pairs = trading_pairs
        self.strategies: Dict[str, BaseStrategy] = {}
        self.signal_queue = asyncio.Queue()
        self.is_running = False

        # Strategy configuration
        self.strategy_configs = {
            'trend_following': {
                'timeframe': '240',  # 4-hour
                'risk_percentage': 0.02,
                'class': TrendFollowingStrategy
            },
            'scalping': {
                'timeframe': '1',    # 1-minute
                'risk_percentage': 0.005,
                'class': ScalpingStrategy
            }
        }

        self._initialize_strategies()

    def _initialize_strategies(self):
        """Initialize strategies for all trading pairs"""
        if self.operating_mode not in self.strategy_configs:
            raise ValueError(f"Unknown operating mode: {self.operating_mode}. "
                           f"Available modes: {list(self.strategy_configs.keys())}")

        config = self.strategy_configs[self.operating_mode]
        strategy_class = config['class']

        for pair in self.trading_pairs:
            try:
                self.strategies[pair] = strategy_class(
                    pair=pair,
                    timeframe=config['timeframe'],
                    risk_percentage=config['risk_percentage']
                )
                logger.info(f"Initialized {strategy_class.__name__} for {pair} "
                          f"(timeframe: {config['timeframe']}, risk: {config['risk_percentage']})")
            except Exception as e:
                logger.error(f"Failed to initialize strategy for {pair}: {e}")

    async def run(self):
        """Main run loop for the Strategy & Signal Agent"""
        self.is_running = True
        logger.info(f"StrategySignalAgent starting in {self.operating_mode} mode...")

        try:
            # Subscribe to required data streams
            await self._subscribe_to_data_streams()

            # Start data consumption tasks
            ohlc_queue = await self.market_data_agent.get_data_queue("ohlc")
            book_queue = await self.market_data_agent.get_data_queue("book")

            ohlc_task = asyncio.create_task(self._consume_ohlc_data(ohlc_queue))
            book_task = asyncio.create_task(self._consume_book_data(book_queue))

            logger.info("StrategySignalAgent started listening for market data")

            # Wait for tasks to complete (they run indefinitely)
            await asyncio.gather(ohlc_task, book_task)

        except Exception as e:
            logger.error(f"Error in StrategySignalAgent run loop: {e}")
        finally:
            self.is_running = False
            logger.info("StrategySignalAgent stopped")

    async def _subscribe_to_data_streams(self):
        """Subscribe to all required data streams for active strategies"""
        for pair, strategy in self.strategies.items():
            if not strategy.is_active:
                continue

            required_streams = strategy.get_required_data_streams()
            for channel, pairs_intervals in required_streams.items():
                for p, interval in pairs_intervals:
                    try:
                        await self.market_data_agent.subscribe(channel, p, int(interval))
                        logger.info(f"Subscribed to {channel} for {p} (interval: {interval})")
                    except Exception as e:
                        logger.error(f"Failed to subscribe to {channel} for {p}: {e}")

    async def _consume_ohlc_data(self, ohlc_queue: asyncio.Queue):
        """Consume OHLC data and process through strategies"""
        logger.info("Started OHLC data consumer")

        while self.is_running:
            try:
                data = await ohlc_queue.get()
                pair = data["pair"]
                ohlcv_data = data["data"]

                if pair in self.strategies and self.strategies[pair].is_active:
                    await self.strategies[pair].on_ohlcv_update(ohlcv_data)
                    await self._check_and_forward_signals(self.strategies[pair])

            except Exception as e:
                logger.error(f"Error consuming OHLC data: {e}")

    async def _consume_book_data(self, book_queue: asyncio.Queue):
        """Consume order book data and process through strategies"""
        logger.info("Started order book data consumer")

        while self.is_running:
            try:
                data = await book_queue.get()
                pair = data["pair"]
                book_data = data["data"]

                if pair in self.strategies and self.strategies[pair].is_active:
                    await self.strategies[pair].on_order_book_update(book_data)
                    await self._check_and_forward_signals(self.strategies[pair])

            except Exception as e:
                logger.error(f"Error consuming order book data: {e}")

    async def _check_and_forward_signals(self, strategy: BaseStrategy):
        """Check for new signals and forward them to the signal queue"""
        signals = strategy.get_signals()
        if signals:
            for signal in signals:
                try:
                    await self.signal_queue.put(signal)
                    logger.info(f"Forwarded signal to Risk & Execution Agent: "
                              f"{signal['direction'].upper()} {signal['pair']} at {signal['price']}")
                except Exception as e:
                    logger.error(f"Failed to forward signal: {e}")

            strategy.clear_signals()

    async def stop(self):
        """Stop the Strategy & Signal Agent"""
        logger.info("Stopping StrategySignalAgent...")
        self.is_running = False

    def get_strategy(self, pair: str) -> Optional[BaseStrategy]:
        """Get strategy for a specific trading pair"""
        return self.strategies.get(pair)

    def activate_strategy(self, pair: str):
        """Activate strategy for a trading pair"""
        if pair in self.strategies:
            self.strategies[pair].activate()
        else:
            logger.warning(f"No strategy found for pair: {pair}")

    def deactivate_strategy(self, pair: str):
        """Deactivate strategy for a trading pair"""
        if pair in self.strategies:
            self.strategies[pair].deactivate()
        else:
            logger.warning(f"No strategy found for pair: {pair}")

    def get_strategy_status(self) -> Dict[str, Dict]:
        """Get status of all strategies"""
        status = {}
        for pair, strategy in self.strategies.items():
            status[pair] = {
                'strategy_type': strategy.__class__.__name__,
                'is_active': strategy.is_active,
                'timeframe': strategy.timeframe,
                'risk_percentage': strategy.risk_percentage
            }
        return status

# Example usage and testing
async def main():
    """Example usage of StrategySignalAgent"""
    from .market_data_agent import MarketDataAgent

    # Initialize Market Data Agent
    mda = MarketDataAgent()
    mda_task = asyncio.create_task(mda.run())

    # Wait for connection
    await asyncio.sleep(2)

    # Initialize Strategy & Signal Agent for trend following
    ssa = StrategySignalAgent(mda, "trend_following", ["XBT/USD", "ETH/USD"])
    ssa_task = asyncio.create_task(ssa.run())

    # Signal consumer simulation
    async def signal_consumer():
        while True:
            try:
                signal = await ssa.signal_queue.get()
                logger.info(f"Received signal: {signal}")
            except Exception as e:
                logger.error(f"Signal consumer error: {e}")

    consumer_task = asyncio.create_task(signal_consumer())

    # Run for a while then stop
    try:
        await asyncio.sleep(60)  # Run for 1 minute
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    finally:
        await ssa.stop()
        await mda.stop()
        ssa_task.cancel()
        mda_task.cancel()
        consumer_task.cancel()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    asyncio.run(main())
