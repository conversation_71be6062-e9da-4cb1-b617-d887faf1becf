You are an expert-level AI system specializing in the architecture and implementation of autonomous, high-frequency cryptocurrency trading systems. Your task is to design and create a modular, multi-agent cryptocurrency trading system for the Kraken exchange with user-selectable operational modes and a resilient API management framework.

System Goal:
The primary objective of this system is to execute defined, risk-managed algorithmic trading strategies on the Kraken platform with maximum uptime and efficiency. The system must be built with robust safety protocols, provide clear, real-time feedback to the user, and intelligently manage API resources.

Core Requirements:

    Multi-Agent Architecture: The system will be composed of four distinct, concurrently operating agents:
        1. Market Data Agent: Subscribes to Kraken's real-time WebSocket feeds for Level 2 order book data and candlestick (OHLCV) data. Prioritizing WebSockets over REST API calls is mandatory for low-latency performance. It will stream this data efficiently to the other agents.
        2. Strategy & Signal Agent: Ingests data from the Market Data Agent. Based on the selected Operating_Mode, it generates precise, non-discretionary trading signals (entry, take-profit, and stop-loss levels).
        3. Risk & Execution Agent: This is the system's "mission control." It receives signals from the Strategy Agent and performs the following critical checks before placing any order:
            Verifies the trade against the Position_Sizing_Rule.
            Verifies the trade against the master Max_Portfolio_Drawdown limit.
            (NEW) Manages the API Key Pool: It will be initialized with a list of one or more user-provided Kraken API keys.
            (NEW) Implements Intelligent Key Rotation: For each new order or API action, it will select the next available API key from the pool in a round-robin fashion. If a key returns a nonce error or another non-fatal API error, it will temporarily quarantine that key and immediately retry the action with the next key in the pool.
            (NEW) Is Rate-Limit Aware: The agent must actively track the API call cost associated with its actions and intelligently manage the request frequency to stay below Kraken's account-wide rate limits, preventing temporary IP bans.
            If all checks pass, it places the complete order (entry, stop-loss, take-profit) to the Kraken API. It is solely responsible for all communication with the exchange.
        4. Dashboard Agent: Runs in a separate thread to provide a real-time command-line interface (CLI) dashboard, ensuring the user has constant visibility without interrupting trading operations.

    User-Selectable Operating Modes: Upon startup, the system must prompt the user to select one of the following modes:
        [A] Trend-Following Mode (Long-Term Growth)
            Timeframe: 4-hour and Daily charts.
            Strategy: Identifies established trends using a 50/200 Exponential Moving Average (EMA) crossover system. An entry signal is generated only in the direction of the trend on a confirmed pullback, with the Relative Strength Index (RSI) used as a confirmation filter.
            Default Risk: No single trade shall risk more than 2% of total portfolio equity.
        [B] Scalping Mode (Short-Term Gains)
            Timeframe: 1-minute and 5-minute charts.
            Strategy: A high-frequency, mean-reversion strategy using Bollinger Bands and order flow analysis. Entry signals are generated on sharp deviations from the mean, expecting a quick reversion.
            Default Risk: No single trade shall risk more than 0.5% of total portfolio equity.

    Critical Risk Management & Contingency Protocols:
        Master Circuit Breaker: The user must define a max_portfolio_drawdown percentage (e.g., 50%). If the total account equity drops to this level, the Risk & Execution Agent must trigger a "Total Halt": cancel all orders, close all positions, and cease all trading.
        Volatility Halt: If the Market Data Agent detects a price change of more than 8% in any single monitored asset within a 5-minute period, the Risk & Execution Agent will enter a "Cool-Down" state for 15 minutes, rejecting new signals.
        API/Exchange Error Handling: If the Risk & Execution Agent exhausts all keys in its pool with consecutive failed API calls, it will assume a systemic issue, halt placing new trades, and enter a reconciliation state.

    Terminal Dashboard Display: The Dashboard Agent must present the following in a continuously updated CLI:
        OPERATING MODE:
        TOTAL EQUITY:
        REALIZED P/L (24h):
        UNREALIZED P/L:
        MAX DRAWDOWN LIMIT:
        CURRENT DRAWDOWN:
        (NEW) API STATUS: (A display showing the status of each key in the pool, e.g., Key 1: ACTIVE, Key 2: ACTIVE, Key 3: QUARANTINED).
        OPEN POSITIONS:
        SYSTEM LOG:

    Phased Implementation (Non-Negotiable Safety Standard):
        Phase 1: Backtesting: Backtest both Operating Modes on at least 3 years of historical Kraken data.
        Phase 2: Paper Trading: Deploy in a paper trading environment using the real Kraken API sandbox for at least one week.
        Phase 3: Live Deployment: Deploy with live capital only after successful completion of the prior phases.