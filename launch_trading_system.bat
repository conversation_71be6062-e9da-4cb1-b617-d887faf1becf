@echo off
title Kraken Multi-Agent Live Trading System
color 0C

:start
echo.
echo ================================================================================
echo                    KRAKEN MULTI-AGENT LIVE TRADING SYSTEM
echo                          ⚠️  REAL MONEY AT RISK ⚠️
echo ================================================================================
echo.
echo Select an option:
echo   1. Quick Setup Check (View current configuration)
echo   2. Configure Trading System (Add/edit API keys and settings)
echo   3. Test Live API Connection (Verify real account access)
echo   4. Launch Live Trading System
echo   5. Launch Live Dashboard Only
echo   6. Run System Tests
echo   7. Exit
echo.

set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto quicksetup
if "%choice%"=="2" goto configure
if "%choice%"=="3" goto testapi
if "%choice%"=="4" goto trading
if "%choice%"=="5" goto dashboard
if "%choice%"=="6" goto tests
if "%choice%"=="7" goto exit
echo Invalid choice. Please try again.
pause
goto start

:quicksetup
echo.
echo ================================================================================
echo                           QUICK SETUP CHECK
echo ================================================================================
echo.
python quick_setup.py
pause
goto start

:configure
echo.
echo ================================================================================
echo                           TRADING SYSTEM CONFIGURATOR
echo ================================================================================
echo.
python trading_configurator.py
pause
goto start

:testapi
echo.
echo ================================================================================
echo                            LIVE API CONNECTION TEST
echo ================================================================================
echo.
echo This will test your API connection with your real Kraken account.
echo.
python test_live_api.py
pause
goto start

:dashboard
echo.
echo ================================================================================
echo                            LIVE TRADING DASHBOARD
echo ================================================================================
echo.
python launch_dashboard.py
pause
goto start

:tests
echo.
echo ================================================================================
echo                              SYSTEM TESTS
echo ================================================================================
echo.
python test_system.py
pause
goto start

:trading
echo.
echo ================================================================================
echo                           LIVE TRADING SYSTEM
echo ================================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✓ Python detected
python --version

REM Check configuration
echo.
echo Checking configuration...
python check_config.py --quiet

if errorlevel 1 (
    echo.
    echo ❌ Configuration check failed!
    echo.
    echo Your .env file is missing or incomplete.
    echo Please configure your API keys first.
    echo.
    set /p setup_choice="Would you like to run the configurator now? (y/n): "
    if /i "%setup_choice%"=="y" (
        echo.
        echo Running trading configurator...
        python trading_configurator.py
        echo.
        echo Please restart the launcher after configuration.
        pause
        exit /b 0
    ) else (
        echo.
        echo Please run: python trading_configurator.py
        echo Then restart this launcher.
        pause
        exit /b 1
    )
)

echo ✓ Configuration validated

REM Install/upgrade pip
echo.
echo Installing/upgrading pip...
python -m pip install --upgrade pip

REM Install requirements
echo.
echo Installing required packages...
python -m pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ❌ ERROR: Failed to install requirements
    echo Please check your internet connection and try again
    pause
    exit /b 1
)

echo ✓ All packages installed successfully

REM Run system tests
echo.
echo Running system tests...
python test_system.py

if errorlevel 1 (
    echo.
    echo ❌ ERROR: System tests failed
    echo Please check the error messages above
    pause
    exit /b 1
)

echo ✓ System tests passed

REM Final warning before live trading
echo.
echo ================================================================================
echo                              ⚠️  FINAL WARNING ⚠️
echo ================================================================================
echo.
echo This system will trade with REAL MONEY using your Kraken API keys.
echo.
echo Before proceeding, make sure:
echo   ✓ Your API keys are correctly configured in .env
echo   ✓ You understand the risks of algorithmic trading
echo   ✓ You have set appropriate risk limits
echo   ✓ You are prepared for potential losses
echo.
echo ================================================================================
echo.

set /p confirm="Type 'START' to begin live trading (or anything else to exit): "

if /i not "%confirm%"=="START" (
    echo.
    echo Trading cancelled by user. Goodbye!
    pause
    exit /b 0
)

REM Launch the trading system
echo.
echo ================================================================================
echo                        STARTING LIVE TRADING SYSTEM
echo ================================================================================
echo.
echo 🔴 LIVE TRADING MODE ACTIVE 🔴
echo Press Ctrl+C to stop the system safely
echo.

REM Start the main trading system
python main.py

REM If we get here, the system has stopped
echo.
echo ================================================================================
echo                           TRADING SYSTEM STOPPED
echo ================================================================================
echo.
echo The trading system has been stopped.
echo Check the logs directory for detailed information.
echo.
pause
goto start

:exit
echo.
echo Goodbye!
exit /b 0
