@echo off
title Kraken Multi-Agent Live Trading System
color 0C

:start
echo.
echo ================================================================================
echo                    KRAKEN MULTI-AGENT LIVE TRADING SYSTEM
echo                          ⚠️  REAL MONEY AT RISK ⚠️
echo ================================================================================
echo.
echo Select an option:
echo   1. Configure Trading System (First time setup)
echo   2. Launch Live Trading System
echo   3. Launch Live Dashboard Only
echo   4. Run System Tests
echo   5. Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto configure
if "%choice%"=="2" goto trading
if "%choice%"=="3" goto dashboard
if "%choice%"=="4" goto tests
if "%choice%"=="5" goto exit
echo Invalid choice. Please try again.
pause
goto start

:configure
echo.
echo ================================================================================
echo                           TRADING SYSTEM CONFIGURATOR
echo ================================================================================
echo.
python trading_configurator.py
pause
goto start

:dashboard
echo.
echo ================================================================================
echo                            LIVE TRADING DASHBOARD
echo ================================================================================
echo.
python launch_dashboard.py
pause
goto start

:tests
echo.
echo ================================================================================
echo                              SYSTEM TESTS
echo ================================================================================
echo.
python test_system.py
pause
goto start

:trading
echo.
echo ================================================================================
echo                           LIVE TRADING SYSTEM
echo ================================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✓ Python detected
python --version

REM Check if .env file exists
if not exist ".env" (
    echo.
    echo ❌ ERROR: .env file not found!
    echo Please edit the .env file with your 5 Kraken API keys before running.
    echo.
    echo The .env file should contain:
    echo   KRAKEN_API_KEY_1=your_first_api_key
    echo   KRAKEN_API_SECRET_1=your_first_api_secret
    echo   ... (and so on for keys 2-5)
    echo.
    pause
    exit /b 1
)

echo ✓ Configuration file found

REM Install/upgrade pip
echo.
echo Installing/upgrading pip...
python -m pip install --upgrade pip

REM Install requirements
echo.
echo Installing required packages...
python -m pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ❌ ERROR: Failed to install requirements
    echo Please check your internet connection and try again
    pause
    exit /b 1
)

echo ✓ All packages installed successfully

REM Run system tests
echo.
echo Running system tests...
python test_system.py

if errorlevel 1 (
    echo.
    echo ❌ ERROR: System tests failed
    echo Please check the error messages above
    pause
    exit /b 1
)

echo ✓ System tests passed

REM Final warning before live trading
echo.
echo ================================================================================
echo                              ⚠️  FINAL WARNING ⚠️
echo ================================================================================
echo.
echo This system will trade with REAL MONEY using your Kraken API keys.
echo.
echo Before proceeding, make sure:
echo   ✓ Your API keys are correctly configured in .env
echo   ✓ You understand the risks of algorithmic trading
echo   ✓ You have set appropriate risk limits
echo   ✓ You are prepared for potential losses
echo.
echo ================================================================================
echo.

set /p confirm="Type 'START' to begin live trading (or anything else to exit): "

if /i not "%confirm%"=="START" (
    echo.
    echo Trading cancelled by user. Goodbye!
    pause
    exit /b 0
)

REM Launch the trading system
echo.
echo ================================================================================
echo                        STARTING LIVE TRADING SYSTEM
echo ================================================================================
echo.
echo 🔴 LIVE TRADING MODE ACTIVE 🔴
echo Press Ctrl+C to stop the system safely
echo.

REM Start the main trading system
python main.py

REM If we get here, the system has stopped
echo.
echo ================================================================================
echo                           TRADING SYSTEM STOPPED
echo ================================================================================
echo.
echo The trading system has been stopped.
echo Check the logs directory for detailed information.
echo.
pause
goto start

:exit
echo.
echo Goodbye!
exit /b 0
