import os
import logging
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

class TradingConfig:
    """Configuration class for the crypto trading system"""
    
    def __init__(self):
        # Load environment variables
        load_dotenv()
        
        # API Configuration
        self.kraken_api_key = os.getenv("KRAKEN_API_KEY", "")
        self.kraken_api_secret = os.getenv("KRAKEN_API_SECRET", "")
        self.kraken_sandbox_api_key = os.getenv("KRAKEN_SANDBOX_API_KEY", "")
        self.kraken_sandbox_api_secret = os.getenv("KRAKEN_SANDBOX_API_SECRET", "")
        
        # Trading Configuration
        self.use_sandbox = os.getenv("USE_SANDBOX", "True").lower() == "true"
        self.max_portfolio_drawdown = float(os.getenv("MAX_PORTFOLIO_DRAWDOWN", "0.50"))
        self.operating_mode = os.getenv("OPERATING_MODE", "trend_following")
        self.trading_pairs = os.getenv("TRADING_PAIRS", "XBT/USD,ETH/USD").split(",")
        
        # Risk Management
        self.max_position_size = float(os.getenv("MAX_POSITION_SIZE", "0.1"))  # 10% of portfolio
        self.stop_loss_percentage = float(os.getenv("STOP_LOSS_PERCENTAGE", "0.02"))  # 2%
        self.take_profit_percentage = float(os.getenv("TAKE_PROFIT_PERCENTAGE", "0.04"))  # 4%
        
        # System Configuration
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.websocket_url = os.getenv("WEBSOCKET_URL", "wss://ws.kraken.com/")
        self.api_rate_limit = int(os.getenv("API_RATE_LIMIT", "20"))  # requests per minute
        
        # Dashboard Configuration
        self.dashboard_refresh_rate = float(os.getenv("DASHBOARD_REFRESH_RATE", "1.0"))  # seconds
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self):
        """Validate configuration parameters"""
        errors = []
        
        # Check API keys
        if not self.use_sandbox:
            if not self.kraken_api_key or not self.kraken_api_secret:
                errors.append("Production API keys are required when USE_SANDBOX=False")
        else:
            if not self.kraken_sandbox_api_key or not self.kraken_sandbox_api_secret:
                errors.append("Sandbox API keys are required when USE_SANDBOX=True")
        
        # Validate numeric ranges
        if not 0 < self.max_portfolio_drawdown <= 1:
            errors.append("MAX_PORTFOLIO_DRAWDOWN must be between 0 and 1")
        
        if not 0 < self.max_position_size <= 1:
            errors.append("MAX_POSITION_SIZE must be between 0 and 1")
        
        if self.stop_loss_percentage <= 0:
            errors.append("STOP_LOSS_PERCENTAGE must be positive")
        
        if self.take_profit_percentage <= 0:
            errors.append("TAKE_PROFIT_PERCENTAGE must be positive")
        
        # Validate operating mode
        valid_modes = ["trend_following", "scalping"]
        if self.operating_mode not in valid_modes:
            errors.append(f"OPERATING_MODE must be one of: {valid_modes}")
        
        # Validate trading pairs
        if not self.trading_pairs or not all(pair.strip() for pair in self.trading_pairs):
            errors.append("TRADING_PAIRS must contain at least one valid pair")
        
        if errors:
            error_msg = "Configuration validation failed:\n" + "\n".join(f"- {error}" for error in errors)
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        logger.info("Configuration validation passed")
    
    def get_api_keys(self) -> List[tuple]:
        """Get API key pairs based on sandbox setting"""
        if self.use_sandbox:
            return [(self.kraken_sandbox_api_key, self.kraken_sandbox_api_secret)]
        else:
            return [(self.kraken_api_key, self.kraken_api_secret)]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary (excluding sensitive data)"""
        return {
            "use_sandbox": self.use_sandbox,
            "max_portfolio_drawdown": self.max_portfolio_drawdown,
            "operating_mode": self.operating_mode,
            "trading_pairs": self.trading_pairs,
            "max_position_size": self.max_position_size,
            "stop_loss_percentage": self.stop_loss_percentage,
            "take_profit_percentage": self.take_profit_percentage,
            "log_level": self.log_level,
            "websocket_url": self.websocket_url,
            "api_rate_limit": self.api_rate_limit,
            "dashboard_refresh_rate": self.dashboard_refresh_rate
        }

def load_config() -> TradingConfig:
    """Load and return trading configuration"""
    return TradingConfig()

def create_env_template():
    """Create a template .env file with all required variables"""
    template_content = """# Kraken API Configuration
KRAKEN_API_KEY=your_production_api_key_here
KRAKEN_API_SECRET=your_production_api_secret_here
KRAKEN_SANDBOX_API_KEY=your_sandbox_api_key_here
KRAKEN_SANDBOX_API_SECRET=your_sandbox_api_secret_here

# Trading Configuration
USE_SANDBOX=True
MAX_PORTFOLIO_DRAWDOWN=0.50
OPERATING_MODE=trend_following
TRADING_PAIRS=XBT/USD,ETH/USD

# Risk Management
MAX_POSITION_SIZE=0.1
STOP_LOSS_PERCENTAGE=0.02
TAKE_PROFIT_PERCENTAGE=0.04

# System Configuration
LOG_LEVEL=INFO
WEBSOCKET_URL=wss://ws.kraken.com/
API_RATE_LIMIT=20

# Dashboard Configuration
DASHBOARD_REFRESH_RATE=1.0
"""
    
    env_file = ".env.template"
    with open(env_file, "w") as f:
        f.write(template_content)
    
    logger.info(f"Created environment template: {env_file}")
    return env_file
