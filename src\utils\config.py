import os
import logging
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

class TradingConfig:
    """Configuration class for the crypto trading system"""

    def __init__(self):
        # Load environment variables
        load_dotenv()

        # API Configuration - 5 Kraken API keys for live trading
        self.kraken_api_keys = []
        self.kraken_api_secrets = []

        for i in range(1, 6):  # API keys 1-5
            api_key = os.getenv(f"KRAKEN_API_KEY_{i}", "")
            api_secret = os.getenv(f"KRAKEN_API_SECRET_{i}", "")
            if api_key and api_secret:
                self.kraken_api_keys.append(api_key)
                self.kraken_api_secrets.append(api_secret)

        # Trading Configuration - LIVE TRADING ONLY
        self.max_portfolio_drawdown = float(os.getenv("MAX_PORTFOLIO_DRAWDOWN", "0.50"))
        self.operating_mode = os.getenv("OPERATING_MODE", "trend_following")
        self.trading_pairs = os.getenv("TRADING_PAIRS", "XBT/USD,ETH/USD").split(",")

        # Risk Management
        self.max_position_size = float(os.getenv("MAX_POSITION_SIZE", "0.1"))  # 10% of portfolio
        self.stop_loss_percentage = float(os.getenv("STOP_LOSS_PERCENTAGE", "0.02"))  # 2%
        self.take_profit_percentage = float(os.getenv("TAKE_PROFIT_PERCENTAGE", "0.04"))  # 4%

        # System Configuration
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.websocket_url = os.getenv("WEBSOCKET_URL", "wss://ws.kraken.com/")
        self.api_rate_limit = int(os.getenv("API_RATE_LIMIT", "20"))  # requests per minute

        # Dashboard Configuration
        self.dashboard_refresh_rate = float(os.getenv("DASHBOARD_REFRESH_RATE", "1.0"))  # seconds

        # Initial Portfolio Value
        self.initial_portfolio_value = float(os.getenv("INITIAL_PORTFOLIO_VALUE", "10000.0"))

        # Validate configuration
        self._validate_config()
    
    def _validate_config(self):
        """Validate configuration parameters"""
        errors = []

        # Check API keys - require at least one valid pair
        if len(self.kraken_api_keys) == 0:
            errors.append("At least one Kraken API key pair is required for live trading")

        if len(self.kraken_api_keys) != len(self.kraken_api_secrets):
            errors.append("Number of API keys must match number of API secrets")
        
        # Validate numeric ranges
        if not 0 < self.max_portfolio_drawdown <= 1:
            errors.append("MAX_PORTFOLIO_DRAWDOWN must be between 0 and 1")
        
        if not 0 < self.max_position_size <= 1:
            errors.append("MAX_POSITION_SIZE must be between 0 and 1")
        
        if self.stop_loss_percentage <= 0:
            errors.append("STOP_LOSS_PERCENTAGE must be positive")
        
        if self.take_profit_percentage <= 0:
            errors.append("TAKE_PROFIT_PERCENTAGE must be positive")
        
        # Validate operating mode
        valid_modes = ["trend_following", "scalping"]
        if self.operating_mode not in valid_modes:
            errors.append(f"OPERATING_MODE must be one of: {valid_modes}")
        
        # Validate trading pairs
        if not self.trading_pairs or not all(pair.strip() for pair in self.trading_pairs):
            errors.append("TRADING_PAIRS must contain at least one valid pair")
        
        if errors:
            error_msg = "Configuration validation failed:\n" + "\n".join(f"- {error}" for error in errors)
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        logger.info("Configuration validation passed")
    
    def get_api_keys(self) -> List[tuple]:
        """Get all API key pairs for live trading"""
        return list(zip(self.kraken_api_keys, self.kraken_api_secrets))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary (excluding sensitive data)"""
        return {
            "live_trading": True,
            "api_keys_count": len(self.kraken_api_keys),
            "max_portfolio_drawdown": self.max_portfolio_drawdown,
            "operating_mode": self.operating_mode,
            "trading_pairs": self.trading_pairs,
            "max_position_size": self.max_position_size,
            "stop_loss_percentage": self.stop_loss_percentage,
            "take_profit_percentage": self.take_profit_percentage,
            "log_level": self.log_level,
            "websocket_url": self.websocket_url,
            "api_rate_limit": self.api_rate_limit,
            "dashboard_refresh_rate": self.dashboard_refresh_rate,
            "initial_portfolio_value": self.initial_portfolio_value
        }

def load_config() -> TradingConfig:
    """Load and return trading configuration"""
    return TradingConfig()

def create_env_template():
    """Create a template .env file with all required variables"""
    template_content = """# Kraken Multi-Agent Live Trading System Configuration
# ⚠️  LIVE TRADING ONLY - REAL MONEY AT RISK ⚠️

# =============================================================================
# KRAKEN API CONFIGURATION - LIVE TRADING
# =============================================================================
# Add your 5 Kraken API keys and secrets here
# Get them from: https://www.kraken.com/u/security/api
# Make sure they have trading permissions enabled

KRAKEN_API_KEY_1=your_first_api_key_here
KRAKEN_API_SECRET_1=your_first_api_secret_here

KRAKEN_API_KEY_2=your_second_api_key_here
KRAKEN_API_SECRET_2=your_second_api_secret_here

KRAKEN_API_KEY_3=your_third_api_key_here
KRAKEN_API_SECRET_3=your_third_api_secret_here

KRAKEN_API_KEY_4=your_fourth_api_key_here
KRAKEN_API_SECRET_4=your_fourth_api_secret_here

KRAKEN_API_KEY_5=your_fifth_api_key_here
KRAKEN_API_SECRET_5=your_fifth_api_secret_here

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================

# Maximum portfolio drawdown before emergency halt (0.0 to 1.0)
MAX_PORTFOLIO_DRAWDOWN=0.50

# Operating mode: "trend_following" or "scalping"
OPERATING_MODE=trend_following

# Trading pairs (comma-separated)
TRADING_PAIRS=XBT/USD,ETH/USD

# Initial portfolio value for tracking (USD)
INITIAL_PORTFOLIO_VALUE=10000.0

# =============================================================================
# RISK MANAGEMENT
# =============================================================================

# Maximum position size as percentage of portfolio (0.0 to 1.0)
MAX_POSITION_SIZE=0.1

# Stop loss percentage (0.0 to 1.0)
STOP_LOSS_PERCENTAGE=0.02

# Take profit percentage (0.0 to 1.0)
TAKE_PROFIT_PERCENTAGE=0.04

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================

# Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Kraken WebSocket URL
WEBSOCKET_URL=wss://ws.kraken.com/

# API rate limit (requests per minute)
API_RATE_LIMIT=20

# Dashboard refresh rate (seconds)
DASHBOARD_REFRESH_RATE=1.0
"""

    env_file = ".env.template"
    with open(env_file, "w") as f:
        f.write(template_content)

    logger.info(f"Created environment template: {env_file}")
    return env_file
