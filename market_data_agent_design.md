# Market Data Agent: Design for Data Ingestion and Streaming

## 1. Overview

The Market Data Agent (MDA) is the foundational component of the Kraken Multi-Agent Cryptocurrency Trading System, responsible for establishing and maintaining real-time data feeds from the Kraken exchange. Its primary objective is to ingest raw market data, process it into a standardized format, and efficiently stream it to other agents within the system, particularly the Strategy & Signal Agent. Given the high-frequency nature of the trading system, the MDA prioritizes low-latency data acquisition and robust handling of WebSocket connections.

## 2. Data Sources and Prioritization

The Kraken exchange offers various methods for accessing market data, including REST API endpoints and WebSocket feeds. For this high-frequency trading system, WebSockets are the preferred and mandatory method for real-time data ingestion due to their persistent, low-latency, and push-based nature. REST API calls will be used sparingly, primarily for initial data synchronization (e.g., fetching historical data for backtesting or initial order book snapshots) or in scenarios where WebSocket connectivity is temporarily unavailable.

- **Primary Data Source**: Kraken WebSocket API
    - **Level 2 Order Book Data**: Provides real-time updates on bids and asks, crucial for understanding market depth and liquidity. The MDA will subscribe to the `book` channel for selected trading pairs.
    - **Candlestick (OHLCV) Data**: Delivers aggregated price data over specific timeframes (e.g., 1-minute, 5-minute, 4-hour, daily). The MDA will subscribe to the `ohlc` channel for relevant timeframes and trading pairs.

- **Secondary Data Source (Limited Use)**: Kraken REST API
    - **Initial Order Book Snapshot**: Used to reconstruct the full order book state upon connection or reconnection to the WebSocket feed.
    - **Historical Data**: For backtesting purposes, historical OHLCV data will be fetched via the REST API.

## 3. Data Ingestion Mechanism (WebSocket)

The MDA will establish and manage persistent WebSocket connections to Kraken. The ingestion mechanism will involve the following steps:

1.  **Connection Establishment**: Upon initialization, the MDA will attempt to establish WebSocket connections to the Kraken public and private WebSocket endpoints (if private data, like user trades, is later required). Robust error handling and reconnection logic will be implemented to ensure continuous data flow.
2.  **Subscription Management**: The MDA will dynamically subscribe to the necessary channels (e.g., `book`, `ohlc`) for specified trading pairs and timeframes. This will be configurable, allowing users to select the assets and data types relevant to their chosen operating mode.
3.  **Message Parsing**: Raw JSON messages received from the WebSocket will be parsed and validated. The MDA will be responsible for deserializing the JSON payloads into structured data objects.
4.  **Order Book Reconstruction**: For Level 2 order book data, the MDA will maintain an in-memory representation of the order book. Incremental updates received via WebSocket (add, remove, change) will be applied to this local order book. A mechanism to request full order book snapshots via REST API will be in place to handle desynchronization or initial state population.
5.  **Data Normalization**: All ingested data, regardless of its source (WebSocket or REST), will be normalized into a consistent internal data format. This ensures that downstream agents receive data in a predictable and easily consumable structure.

## 4. Data Streaming Mechanism (Inter-Agent Communication)

The MDA will act as a central data hub, streaming processed market data to other agents. Given the real-time and continuous nature of market data, an efficient inter-process communication (IPC) mechanism is crucial. Python's `multiprocessing` module, specifically `Queue` or `Pipe` objects, are suitable candidates for this purpose, allowing agents to run in separate processes while maintaining efficient data exchange.

1.  **Data Queues/Pipes**: The MDA will maintain separate output queues or pipes for each type of data (e.g., order book updates, OHLCV candles) or for each subscribing agent. This allows for asynchronous data delivery and prevents blocking.
2.  **Publisher-Subscriber Model (Conceptual)**: While not a formal message broker, the MDA will conceptually operate as a publisher, pushing data to its subscribers (other agents). Agents will register their interest in specific data streams upon their initialization.
3.  **Data Serialization**: Before placing data onto the queues/pipes, data objects will be serialized (e.g., using `pickle` or `json` for more complex objects) to ensure safe and efficient transmission across process boundaries.
4.  **Flow Control**: Mechanisms to handle potential backpressure (e.g., if a consuming agent is slower than the MDA's data output) will be considered to prevent memory exhaustion. This might involve bounded queues or discarding older data if necessary.

## 5. Error Handling and Resilience

-   **WebSocket Reconnection**: Automatic reconnection logic with exponential backoff will be implemented for dropped WebSocket connections.
-   **Data Validation**: Incoming data will be validated for integrity and completeness. Corrupted or malformed messages will be logged and discarded.
-   **Rate Limit Awareness (Passive)**: While the Risk & Execution Agent will actively manage API rate limits for outgoing requests, the MDA will passively monitor its own data request frequency to avoid being rate-limited by Kraken for data subscriptions.
-   **Logging**: Comprehensive logging will be implemented to record connection status, subscription events, data ingestion rates, and any errors encountered.

## 6. Configuration

The MDA's behavior will be configurable via a dedicated configuration file (e.g., `config/market_data_agent.yaml` or `.env` variables). Key configurable parameters will include:

-   Kraken WebSocket URL
-   List of trading pairs to subscribe to (e.g., `XBT/USD`, `ETH/USD`)
-   List of OHLCV timeframes to subscribe to (e.g., `1`, `5`, `240`, `1440` minutes)
-   Logging level

This design ensures that the Market Data Agent is robust, efficient, and provides a reliable data foundation for the entire trading system.

