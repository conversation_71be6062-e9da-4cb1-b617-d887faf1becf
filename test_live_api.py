#!/usr/bin/env python3
"""
Test script to verify live Kraken API integration
This will test the real API calls with your actual account
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent))

async def test_live_api():
    """Test live API integration"""
    
    print("=" * 60)
    print("TESTING LIVE KRAKEN API INTEGRATION")
    print("⚠️  This will make real API calls to your Kraken account")
    print("=" * 60)
    print()
    
    try:
        # Import required modules
        from src.agents.risk_execution_agent import KrakenAPIClient
        from src.utils.config import load_config
        
        # Load configuration
        config = load_config()
        
        if not config.kraken_api_keys:
            print("❌ No API keys configured!")
            print("Please run: python trading_configurator.py")
            return
        
        print(f"✓ Found {len(config.kraken_api_keys)} API keys")
        
        # Test with first API key
        api_key, api_secret = config.kraken_api_keys[0], config.kraken_api_secrets[0]
        client = KrakenAPIClient(api_key, api_secret)
        
        print(f"✓ Created API client with key: {api_key[:8]}...")
        print()
        
        # Test 1: Get account balance
        print("🔍 Testing account balance retrieval...")
        try:
            balances = await client.get_account_balance()
            print("✅ Account balance retrieved successfully!")
            
            total_usd_value = 0.0
            for asset, balance in balances.items():
                if balance > 0:
                    print(f"  {asset}: {balance:.8f}")
                    if asset in ["USD", "ZUSD"]:
                        total_usd_value += balance
                    elif asset in ["XBT", "XXBT"]:
                        # Estimate USD value (rough calculation)
                        total_usd_value += balance * 45000  # Approximate BTC price
                    elif asset in ["ETH", "XETH"]:
                        # Estimate USD value (rough calculation)
                        total_usd_value += balance * 2500   # Approximate ETH price
            
            print(f"\n💰 Estimated total portfolio value: ~${total_usd_value:.2f}")
            
        except Exception as e:
            print(f"❌ Failed to get account balance: {e}")
            return
        
        print()
        
        # Test 2: Get current market prices
        print("📈 Testing market price retrieval...")
        try:
            # Test public API call for BTC price
            btc_result = await client._make_api_request('/0/public/Ticker?pair=XBTUSD', private=False)
            
            if 'XXBTZUSD' in btc_result:
                btc_price = float(btc_result['XXBTZUSD']['c'][0])
            elif 'XBTUSD' in btc_result:
                btc_price = float(btc_result['XBTUSD']['c'][0])
            else:
                btc_price = "Unknown"
            
            print(f"✅ Current BTC/USD price: ${btc_price}")
            
        except Exception as e:
            print(f"❌ Failed to get market prices: {e}")
        
        print()
        
        # Test 3: Get open orders
        print("📋 Testing open orders retrieval...")
        try:
            open_orders = await client.get_open_orders()
            order_count = len(open_orders.get("open", {}))
            print(f"✅ Open orders retrieved: {order_count} orders")
            
        except Exception as e:
            print(f"❌ Failed to get open orders: {e}")
        
        print()
        
        # Summary
        print("=" * 60)
        print("✅ LIVE API INTEGRATION TEST COMPLETED")
        print("=" * 60)
        print()
        print("The system is now using REAL Kraken API calls:")
        print("✓ Account balances are retrieved from your live account")
        print("✓ Market prices are fetched in real-time")
        print("✓ Portfolio equity calculations use live data")
        print("⚠️  All trading will be with REAL MONEY!")
        print()
        print("Your actual portfolio value will now be displayed correctly")
        print("in the trading dashboard.")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please install dependencies: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_live_api())
