#!/usr/bin/env python3
"""
Installation and setup script for Kraken Multi-Agent Trading System
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_step(step, description):
    """Print a formatted step"""
    print(f"\n[{step}] {description}")

def run_command(command, description=""):
    """Run a command and handle errors"""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error {description}: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"Error: Python 3.8+ required, but you have {version.major}.{version.minor}")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_pip():
    """Check if pip is available"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✓ pip is available")
        return True
    except subprocess.CalledProcessError:
        print("Error: pip is not available")
        return False

def install_dependencies():
    """Install required dependencies"""
    print_step("3", "Installing dependencies...")
    
    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "upgrading pip"):
        return False
    
    # Install requirements
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "installing requirements"):
        return False
    
    print("✓ Dependencies installed successfully")
    return True

def setup_environment():
    """Set up environment configuration"""
    print_step("4", "Setting up environment configuration...")
    
    env_file = Path(".env")
    template_file = Path(".env.template")
    
    if env_file.exists():
        print("✓ .env file already exists")
        return True
    
    if not template_file.exists():
        print("Error: .env.template file not found")
        return False
    
    # Copy template to .env
    shutil.copy(template_file, env_file)
    print("✓ Created .env file from template")
    
    print("\n" + "!"*60)
    print(" IMPORTANT: CONFIGURE YOUR API KEYS")
    print("!"*60)
    print("1. Edit the .env file with your Kraken API credentials")
    print("2. Set USE_SANDBOX=True for testing (recommended)")
    print("3. Configure your trading parameters")
    print("4. Never commit the .env file to version control!")
    
    return True

def create_directories():
    """Create necessary directories"""
    print_step("5", "Creating directories...")
    
    directories = ["logs", "data", "backups"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    return True

def verify_installation():
    """Verify the installation"""
    print_step("6", "Verifying installation...")
    
    try:
        # Test imports
        import asyncio
        import pandas
        import numpy
        import websockets
        from dotenv import load_dotenv
        
        print("✓ Core dependencies imported successfully")
        
        # Test our modules
        from src.agents.market_data_agent import MarketDataAgent
        from src.agents.strategy_signal_agent import StrategySignalAgent
        from src.agents.risk_execution_agent import RiskExecutionAgent
        from src.agents.dashboard_agent import DashboardAgent
        from src.utils.config import load_config
        from src.utils.logging_config import setup_logging
        
        print("✓ All modules imported successfully")
        
        # Test configuration loading
        if Path(".env").exists():
            try:
                config = load_config()
                print("✓ Configuration loaded successfully")
            except Exception as e:
                print(f"⚠ Configuration loading failed: {e}")
                print("  Please check your .env file configuration")
        
        return True
        
    except ImportError as e:
        print(f"Error: Missing dependency - {e}")
        return False
    except Exception as e:
        print(f"Error during verification: {e}")
        return False

def main():
    """Main installation function"""
    print_header("KRAKEN MULTI-AGENT TRADING SYSTEM INSTALLER")
    
    print("This script will install and configure the Kraken Multi-Agent Trading System.")
    print("Make sure you have Python 3.8+ and pip installed.")
    
    # Step 1: Check Python version
    print_step("1", "Checking Python version...")
    if not check_python_version():
        sys.exit(1)
    
    # Step 2: Check pip
    print_step("2", "Checking pip availability...")
    if not check_pip():
        sys.exit(1)
    
    # Step 3: Install dependencies
    if not install_dependencies():
        print("Installation failed at dependency installation step.")
        sys.exit(1)
    
    # Step 4: Setup environment
    if not setup_environment():
        print("Installation failed at environment setup step.")
        sys.exit(1)
    
    # Step 5: Create directories
    if not create_directories():
        print("Installation failed at directory creation step.")
        sys.exit(1)
    
    # Step 6: Verify installation
    if not verify_installation():
        print("Installation completed but verification failed.")
        print("Please check the error messages above.")
        sys.exit(1)
    
    # Success!
    print_header("INSTALLATION COMPLETED SUCCESSFULLY!")
    
    print("\nNext steps:")
    print("1. Edit the .env file with your Kraken API credentials")
    print("2. Review the configuration in .env")
    print("3. Run the system: python main.py")
    print("\nFor help and documentation, see README.md")
    
    print("\n" + "="*60)
    print(" READY TO TRADE!")
    print("="*60)

if __name__ == "__main__":
    main()
